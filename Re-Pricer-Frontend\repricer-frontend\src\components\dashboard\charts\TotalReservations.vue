<template>
    <Card class="flex-1" v-if="summary">
        <template #content>
            <div class="flex flex-column gap-4">
                <span class="text-secondary text-sm text-nowrap align-items-center d-flex">
                    Total Reservations
                </span>
                <Timeline :value="timeLineDate" :pt="timelineConfig" layout="horizontal">
                    <template #content="slotProps">
                        <div>
                            <small class="p-text-secondary d-block">
                                {{ slotProps.item.status }}
                            </small>
                            <small class="p-text-secondary d-block text-nowrap">
                                {{ slotProps.item.value }} <strong
                                    v-if="slotProps.item.percentage">({{ slotProps.item.percentage.toFixed(2)
                                    }}%)</strong>
                            </small>
                        </div>
                    </template>
                </Timeline>
            </div>
        </template>
    </Card>
    <Card class="flex-1" v-else>
        <template #content>
            <div class="flex flex-column gap-4">
                <span class="text-secondary text-sm text-nowrap align-items-center d-flex">
                    Reservations
                </span>
                <Timeline :value="timeLineDate" :pt="timelineConfig" layout="horizontal">
                    <template #content="slotProps">
                        <div>
                            <small class="p-text-secondary d-block">
                                <Skeleton width="8rem" height="1rem" class="mt-1"></Skeleton>
                            </small>
                            <small class="p-text-secondary d-block">
                                <Skeleton width="8rem" height="1rem" class="mt-1"></Skeleton>
                            </small>
                        </div>
                    </template>
                </Timeline>
            </div>
        </template>
    </Card>
</template>
<script>
import Card from 'primevue/card';
import Skeleton from 'primevue/skeleton';
import Timeline from 'primevue/timeline';

export default {
    name: "TotalReservations",
    components: { Card, Skeleton, Timeline },
    data() {
        return {
            timelineConfig: {
                opposite: {
                    class: "d-none"
                }
            },
        };
    },
    props: ['summary'],
    watch: {},
    computed: {
        timeLineDate() {
            let data = [
                { status: 'Reservations' },         // totalUniqueBookings
                { status: 'Refundable' },           // totalRefundableReservation
                { status: 'Qualified' },            // filteredReservationCount
                // { status: 'Optimizable' },          // lifetimeOptimisedBookingCount
            ];
            if (this.summary && this.summary.summarizedView) {
                let Optimized = 0;
                let totalReservationsCount = 0;
                const total = this.summary.summarizedView.find(s => s.reportType.toLowerCase() == 'total');
                if (total) {
                    Optimized = (total.reservationsCount / this.summary.filteredReservationCount) * 100;
                    totalReservationsCount = total.reservationsCount
                }
                const NonRefundable = (this.summary.totalRefundableReservation / this.summary.totalUniqueBookings) * 100;
                const Qualified = (this.summary.filteredReservationCount / this.summary.totalUniqueBookings) * 100;
                data = [
                    { status: "Reservations", percentage: 0, value: this.summary.totalUniqueBookings, },
                    { status: "Refundable", percentage: NonRefundable, value: this.summary.totalRefundableReservation, },
                    { status: "Qualified", percentage: Qualified, value: this.summary.filteredReservationCount },
                    // { status: "Optimizable", percentage: Optimized, value: totalReservationsCount },
                ]
            }
            return data;
        },
    },
    async mounted() {
    },
    methods: {
    }
}

</script>

<style>
div#invoiceRange_panel {
    min-width: 250px;
}
span#invoiceRange .p-datepicker-trigger {
    border-radius: 0;
    padding: 0;
    width: 100%;
    background: transparent;
    color: #000;
    border: 0
}
</style>