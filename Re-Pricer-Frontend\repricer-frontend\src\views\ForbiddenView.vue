<script>
export default {
	name: "Forbidden",
	components: {  },
	data: () => {
		return {
		}
	},
	computed: {
	},
	methods: {
		
	}
}
</script>

<template>
	<main class="d-flex w-100 p-0">
		<div class="container text-center d-flex flex-column gap-1">
			<div class="fw-bolder fz-r6 text-danger mt-5">403</div>
			<div>Access Denied</div>
			<div>You don't have permission to access this page.</div>
		</div>
	</main>
</template>
