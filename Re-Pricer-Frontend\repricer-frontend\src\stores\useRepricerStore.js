import { defineStore } from 'pinia'
import { useApiStore } from './useApiStore';

export const useRepricerStore = defineStore({
	// Set persist to true
	persist: true,
	id: "repricer",
	state: () => ({
		repricer: {}
	}),
	actions: {
		async getRePricerById(RePricerId) {
			const api = useApiStore();
			const response = await api.get(`/api/Admin/GetRepricer/${RePricerId}`);
			if (response) {
				this.repricer = response
			}
			return response;
		},
		async getCurrency(RePricerId) {
			const api = useApiStore();
			const response = await api.get(`/api/Admin/GetCurrency/${RePricerId}`);
			return response;
		},
		async createRepricer(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/Admin/CreateRepricer`, payload);
			return response;
		},
		async updateRepricer(payload) {
			const api = useApiStore();
			const response = await api.put(`/api/Admin/UpdateRepricer`, payload);
			return response;
		},
		async GetRepricerReport(payload) {
			const api = useApiStore();
			let params = {};
			Object.keys(payload).map(key => {
				if (payload[key]) {
					params[key] = payload[key];
				}
				return key;
			})
			if (payload.hasOwnProperty("isCached"))
				params.isCached = payload.isCached;

			const response = await api.post(`/api/Admin/GetRepricerReport`, params);
			return response;
		},
		async GetRepricerReportSummary(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/Admin/GetRepricerReportSummary`, payload);
			return response;
		},
		async GetSuperAdminReportSummary(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/Admin/GetRepricerReportSummaryNew`, payload);
			return response;
		},
		async GetDateWiseReservationCount(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/Admin/GetDateWiseReservationCount`, payload);
			return response;
		},
		async GetOptimizedReservation(payload) {
			const api = useApiStore();
			const params = [];
			Object.keys(payload).map((key) => {
				params.push(`${key}=${payload[key]}`)
				return key;
			});
			// return "done"
			let actionUrl = `/api/Irix/Optimize`;
			if (payload.prebookSupplier) {
				actionUrl = `/api/Irix/OptimizeMultiSuplier`;
			}
			const response = await api.post(actionUrl + `?` + params.join('&'));
			return response;
		},
		async GetIrixConfiguration(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/Irix/IrixConfiguration?repricerid=${payload}`);
			return response;
		},
		async GetTimeZones() {
			const api = useApiStore();
			const response = await api.get(`/api/Admin/GetTimeZones`);
			return response;
		},
		async GetSupplierCount(repricerId) {
			const api = useApiStore();
			const response = await api.get(`/api/Admin/GetSupplierCount?repricerId=` + repricerId);
			return response;
		},
		async GetInvoiceReport(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/Admin/GetInvoiceReport`, payload);
			return response;
		},
		async SetRepricerStatus(payload) {
			const api = useApiStore();
			const response = await api.put(`/api/Admin/UpdateRepricerStatus/${payload.RePricerId}?isActive=${payload.isActive}`);
			return response;
		},
		async getMultiSupplier(payload) {
			const api = useApiStore();
			const params = [];
			Object.keys(payload).map((key) => {
				params.push(`${key}=${payload[key]}`)
				return key;
			});
			const response = await api.get(`/api/Admin/GetMultiSupplierReservationReport?${params.join('&')}`);
			return response;
		},
		async getAdminOptimizations(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/Admin/GetDailyOptimizationReport`, payload);
			return response;
		},
		async getCountryList(repricerId) {
			const api = useApiStore();
			const response = await api.get(`/api/Admin/GetCountry/${repricerId}`);
			return response;
		},
		async getCityList(repricerId, countryId) {
			const api = useApiStore();
			const response = await api.get(`/api/Admin/GetCity/${repricerId}?ContryId=${countryId}`);
			return response;
		},
		async getCityHotel(repricerId, countryId, cityId) {
			const api = useApiStore();
			const response = await api.post(`/api/Admin/GetHotel`, {
				"rePricerId": repricerId,
				"countryId": countryId,
				"cityId": cityId
			});
			return response;
		},
		async getResellerList(repricerId) {
			const api = useApiStore();
			const response = await api.get(`/api/Admin/GetReseller/${repricerId}`);
			return response;
		},
		async getSupplierList(repricerId) {
			const api = useApiStore();
			const response = await api.get(`/api/Admin/GetSupplier/${repricerId}`);
			return response;
		},
	}
});