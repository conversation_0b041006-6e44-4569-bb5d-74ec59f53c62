<script>
import { useRepricerStore } from '@/stores/useRepricerStore';
import { useUserStore } from '@/stores/useUserStore';
import Button from 'primevue/button';
import Column from 'primevue/column';
import DataTable from 'primevue/datatable';
import Recommendations from '../Recommendations.vue';
import { useAuthStore } from '@/stores/useAuthStore';

export default {
    name: "AdminOptimizations",
    components: {
        Button, Column, DataTable, Recommendations
    },
    data() {
        return {
            isDeveloper: false,
            response: [],
            expandedRows: {},
            optimizationTypes: {
                "0": "Demo",
                "1": "Demo",
                "2": "Semi-Automation",
                "3": "Automation",
            },
        }
    },
    computed: {
    },
    methods: {
        async getAdminOptimizations(isFresh) {
            const repricer = useRepricerStore();
            const user = useUserStore();

            user.users.length == 0 && await user.getAllUsers();
            const params = { "repricerId": 0 };
            if (isFresh) {
                params["isCached"] = false
            } else {
                params["isCached"] = true
            }
            const response = await repricer.getAdminOptimizations(params);

            const arrInvoiceInfo = user.users.map(u => {
                u["id"] = u.rePricerDetail.repricerUserID;
                const currentUserReports = response.filter(r => r.repricerId == u.rePricerDetail.repricerUserID);

                if (currentUserReports.length > 0) {
                    const result = {};
                    currentUserReports?.forEach(item => {
                        result[item.reportType] = item;
                    });
                    u["reports"] = result;
                }
                delete u.userInfo;
                return u
            });

            this.response = arrInvoiceInfo.filter(u => u.reports?.Prebook && u.rePricerDetail.optimizationType == 3);
            console.log("this.response", this.response)
        }
    },
    async mounted() {
        const auth = useAuthStore();
        this.isDeveloper = auth.userInfo.roleName.indexOf("Developer") >= 0;
        await this.getAdminOptimizations();
    },
    watch: {
    }
}
</script>

<template>
    <main class="flex flex-column m-3">
        <div class="flex justify-content-end mb-3">
            <Button v-if="isDeveloper" icon="pi pi-refresh" label="Fetch latest data" @click="getAdminOptimizations(true)"></Button>
        </div>
        <DataTable v-model:expandedRows="expandedRows" :value="response" dataKey="id" class="w-100">
            <Column expander style="width: 5rem" />
            <Column field="name" header="Customer Name">
                <template #body="slotProps">
                    <div>
                        {{ slotProps.data.rePricerDetail.repricerUserName }}
                    </div>
                </template>
            </Column>
            <Column field="name" header="Optimization Type">
                <template #body="slotProps">
                    {{ optimizationTypes[slotProps.data.rePricerDetail.optimizationType] }}
                </template>
            </Column>
            <Column field="name" header="Updated on">
                <template #body="slotProps">
                    <small v-if="slotProps.data.reports?.Prebook">
                        {{ $filters.dateAndTimeLocal(slotProps.data.reports.Prebook.updatedOn) }}
                    </small>
                    <small v-else class="text-muted">No data</small>
                </template>
            </Column>
            <Column field="name" header="Same Supplier">
                <template #body="slotProps">
                    <template v-if="slotProps.data.reports?.Prebook && slotProps.data.reports.Prebook.reservationCountSame > 0">
                        <small class="text-muted">Count: {{ slotProps.data.reports.Prebook.reservationCountSame }}</small>
                        <div>{{ slotProps.data.reports.Prebook.profitSame }} EUR</div>
                    </template>
                    <small v-else class="text-muted">No data</small>
                </template>
            </Column>
            <Column field="name" header="Cross Supplier">
                <template #body="slotProps">
                    <template v-if="slotProps.data.reports?.Prebook && slotProps.data.reports.Prebook.reservationCountMulti">
                        <small class="text-muted">Count: {{ slotProps.data.reports.Prebook.reservationCountMulti }}</small>
                        <div>{{ slotProps.data.reports.Prebook.profitMulti }} EUR</div>
                    </template>
                    <small v-else class="text-muted">No data</small>
                </template>
            </Column>
            <template #expansion="slotProps" class="bg-black-alpha-10">
                <Recommendations :customerID="slotProps.data.rePricerDetail.repricerUserID" :isOnlyActive="true" />
            </template>
        </DataTable>
    </main>
</template>

<style scoped>
tr.p-datatable-row-expansion {
    background-color: rgba(0, 0, 0, .1019607843) !important;
}
</style>