<script>
// Packages
import _ from 'lodash';
import moment from 'moment';
// Stores
import { useAuthStore } from '@/stores/useAuthStore';
import { useRepricerStore } from '@/stores/useRepricerStore';
// components
import ReportFilters from '@/components/repricer/ReportFilters.vue';
import OptimizedNotifications from '@/components/dashboard/OptimizedNotifications.vue';
import Loader from '@/components/Loader.vue';
import Card from 'primevue/card';
import TabMenu from 'primevue/tabmenu';
import Paginator from 'primevue/paginator';

export default {
	name: "Recommendations",
	components: { Loader, Card, TabMenu, OptimizedNotifications, Paginator, ReportFilters },
	props: ['customerID', 'isOnlyActive'],
	data() {
		return {
			isSuperAdmin: false,
			mainLoading: true,
			userInfo: null,
			providers: [],
			preBookResponse: {},
			selectedDatesForPrebook: [], //[new Date(moment().subtract(30, "days")), new Date()],
			active: 0,
			items: [
				{
					label: 'Optimized',
					action: "GetRepricerReport",
					params: {
						isReservationActionTaken: true
					},
					role: ["*"],
					command: async ({ item }) => {
						let path = "/optimizations/optimized";
						if (this.$route.params.repricerId) {
							path = "/optimizations/optimized/" + this.$route.params.repricerId;
						}
						this.pageType = 'optimized';
						this.$router.push({ path: path });
						this.selectedTab = item;
						this.pagination.pageNumber = 1;
						await this.getPreBookData(this.selectedDatesForPrebook, true, 1);
					}
				},
				{
					label: () => this.isSuperAdmin ? 'Active' : 'Optimizations',
					action: "Prebook",
					params: {
						reportType: "Prebook",
					},
					role: ["*"],
					command: async ({ item }) => {
						let path = "/optimizations/active";
						if (this.$route.params.repricerId) {
							path = "/optimizations/active/" + this.$route.params.repricerId;
						}
						this.pageType = 'active';
						this.$router.push({ path: path });
						this.selectedTab = item;
						this.pagination.pageNumber = 1;
						await this.getPreBookData(this.selectedDatesForPrebook, true, 1);
					}
				},
				{
					label: () => this.isSuperAdmin ? 'Cancellation Policy' : 'Gain More - Cancellation Policy',
					action: "CancellationEdgeCase",
					params: {
						reportType: "CancellationEdgeCase",
					},
					role: ["*"],
					command: async ({ item }) => {
						let path = "/optimizations/cancellation-policy";
						if (this.$route.params.repricerId) {
							path = "/optimizations/cancellation-policy/" + this.$route.params.repricerId;
						}
						this.pageType = 'cancellation-policy';
						this.$router.push({ path: path });
						this.selectedTab = item;
						this.pagination.pageNumber = 1;
						await this.getPreBookData(this.selectedDatesForPrebook, true, 1);
					}
				},
				{
					label: () => this.isSuperAdmin ? 'Price Edge' : 'Gain More - Price Threshold', //'',
					action: "PriceEdgeCase",
					params: {
						reportType: "PriceEdgeCase",
					},
					role: ["*"],
					command: async ({ item }) => {
						let path = "/optimizations/price-threshold";
						if (this.$route.params.repricerId) {
							path = "/optimizations/price-threshold/" + this.$route.params.repricerId;
						}
						this.pageType = 'price-threshold';
						this.$router.push({ path: path });
						this.selectedTab = item;
						this.pagination.pageNumber = 1;
						await this.getPreBookData(this.selectedDatesForPrebook, true, 1);
					}
				},
				{
					label: 'Better Cancellation Policy',
					action: "NoOrLessGainButBetterCancellation",
					params: {
						reportType: "NoOrLessGainButBetterCancellation",
					},
					role: ["*"],
					command: async ({ item }) => {
						let path = "/optimizations/cancellation-policy-better";
						if (this.$route.params.repricerId) {
							path = "/optimizations/cancellation-policy-better/" + this.$route.params.repricerId;
						}
						this.pageType = 'cancellation-policy-better';
						this.$router.push({ path: path });
						this.selectedTab = item;
						this.pagination.pageNumber = 1;
						await this.getPreBookData(this.selectedDatesForPrebook, true, 1);
					}
				},
				{
					label: 'Cancellation Policy Applicable',
					action: "CancellationChargesApplicable",
					params: {
						reportType: "CancellationChargesApplicable",
					},
					role: ["*"],
					command: async ({ item }) => {
						let path = "/optimizations/cancellation-policy-applicable";
						if (this.$route.params.repricerId) {
							path = "/optimizations/cancellation-policy-applicable/" + this.$route.params.repricerId;
						}
						this.pageType = 'cancellation-policy-applicable';
						this.$router.push({ path: path });
						this.selectedTab = item;
						this.pagination.pageNumber = 1;
						await this.getPreBookData(this.selectedDatesForPrebook, true, 1);
					}
				},
				{
					label: 'Cross Supplier - Within Criteria',
					action: "UsingRoomMapping",
					params: {
						reportType: "UsingRoomMapping",
					},
					role: [],
					command: async ({ item }) => {
						let path = "/optimizations/using-room-mapping";
						if (this.$route.params.repricerId) {
							path = "/optimizations/using-room-mapping/" + this.$route.params.repricerId;
						}
						this.pageType = 'using-room-mapping';
						this.$router.push({ path: path });
						this.selectedTab = item;
						this.pagination.pageNumber = 1;
						await this.getPreBookData(this.selectedDatesForPrebook, true, 1);
					}
				},
				{
					label: 'Cross Supplier - Outside Criteria',
					action: "UsingRoomMapping1",
					params: {
						reportType: "UsingRoomMapping1",
					},
					role: ["Developer"],
					command: async ({ item }) => {
						let path = "/optimizations/using-room-mapping-without-criteria";
						if (this.$route.params.repricerId) {
							path = "/optimizations/using-room-mapping-without-criteria/" + this.$route.params.repricerId;
						}
						this.pageType = 'using-room-mapping-without-criteria';
						this.$router.push({ path: path });
						this.selectedTab = item;
						this.pagination.pageNumber = 1;
						await this.getPreBookData(this.selectedDatesForPrebook, true, 1);
					}
				}
			],
			invoiceItem: {
				label: 'Invoice',
				action: "GetInvoiceReport",
				params: {
					"reservationId": null,
					"fromDate": null,
					"toDate": null
				}
			},
			reportItem: {
				label: 'Report',
				action: "GetSuperAdminReport",
				params: {
					"reservationId": null,
					"reservationStatus": null,

					"preBookFromDate": null,
					"preBookToDate": null,

					"bookingFromDate": null,
					"bookingToDate": null,

					"checkInFromDate": null,
					"checkInToDate": null,

					"profitFrom": null,
					"profitTo": null,

					"pageNumber": 1,
					"pageSize": 20,
					"suppliers": null,
					"cpDaysGain": null,
					"isPrebookLive": false,
					"isReservationActionTaken": null,
					"reportType": null
				}
			},
			prebookTypes: {
				"active": "Prebook",
				"cancellation-policy": "CancellationEdgeCase",
				"cancellation-policy-applicable": "CancellationChargesApplicable",
				"cancellation-policy-better": "NoOrLessGainButBetterCancellation",
				"price-threshold": "PriceEdgeCase",
				"optimized": "GetRepricerReport",
				"report": "GetSuperAdminReport",
				"invoice": "GetInvoiceReport",
				"using-room-mapping": "UsingRoomMapping",
				"using-room-mapping-without-criteria": "UsingRoomMapping1",
				// "without-room-info": "WithoutRoomInfoMatching",
			},
			tableLoading: false,
			selectedTab: null,
			pagination: {
				pageNumber: 1,
				pageSize: 20,
				template: {
					'640px': 'PrevPageLink CurrentPageReport NextPageLink',
					'960px': 'FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink',
					'1300px': 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink',
					default: 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink' // JumpToPageDropdown JumpToPageInput
				}
			},
			pageType: '',
			prebookRequestParams: null
		}
	},
	computed: {
		roleBasedTabs() {
			return this.items.filter(action => {
				// If the action role is "*" (wildcard), allow for all
				if (action.role.includes("*")) {
					return true;
				}

				// Check if there is an intersection between userRoles and action roles
				return action.role.some(role => this.userInfo.roleName.includes(role));
			});
		},
		getPrebookDateParams() {
			const params = {}
			if (this.selectedDatesForPrebook[0]) {
				params["preBookFromDate"] = moment(this.selectedDatesForPrebook[0]).format("YYYY-MM-DD");
			}
			if (this.selectedDatesForPrebook[1]) {
				params["preBookToDate"] = moment(this.selectedDatesForPrebook[1]).format("YYYY-MM-DD");
			}
			return params
		},
		getNotificationType() {
			if (this.active > 0) {
				return this.items[this.active].action
			} else {
				return "GetSuperAdminReport"
			}
		}
	},
	methods: {
		async getPreBookData(dateFilter, fromTab, pageNumber, isFresh) {
			if (fromTab) {
				this.tableLoading = true;
			} else {
				this.mainLoading = true;
			}

			const repricer = useRepricerStore();
			let actionType = this.prebookTypes[this.$route.params.type];
			const activeIndex = this.items.findIndex(i => i.action == actionType);
			if (activeIndex && activeIndex >= 0) {
				this.active = activeIndex;
			}
			// this.selectedTab = this.items[this.active];
			if (dateFilter && dateFilter.length > 0) {
				this.selectedDatesForPrebook = dateFilter;
			} else {
				this.selectedDatesForPrebook = [];
			}

			const currPageNumber = pageNumber || this.pagination.pageNumber;
			let getPrebookParams = {
				"repricerId": parseInt(this.repricerId),
				...this.getPrebookDateParams,
				...this.selectedTab.params,
				pageNumber: currPageNumber,
				pageSize: this.pagination.pageSize
			};

			this.prebookRequestParams = getPrebookParams;
			// if (getPrebookParams.isReservationActionTaken) {
			// 	getPrebookParams.pageSize = 150
			// }

			if (this.isOnlyActive) {
				//getPrebookParams["preBookFromDate"] = moment().format("YYYY-MM-DD");
				//getPrebookParams["preBookToDate"] = moment().add(1, "days").format("YYYY-MM-DD");
			}

			if (isFresh) {
				getPrebookParams["isCached"] = false
			}
			this.preBookResponse = await repricer.GetRepricerReport(getPrebookParams);
			// this.preBookResponse.pagesSummary.totalRows = this.preBookResponse.pagesSummary.totalRows;

			// if (this.preBookResponse.requestBody.isReservationActionTaken) {
			// 	this.preBookResponse.data = this.preBookResponse.data.filter(booking => {
			// 		let isActionYes = booking.actionsTakens ? booking.actionsTakens.filter(action => action.actionId == 1) : []
			// 		return isActionYes.length > 0
			// 	});
			// }

			this.providers = this.preBookResponse.data && _.uniqBy(this.preBookResponse.data.map(p => p.reservation.supplier));
			this.mainLoading = false;
			this.tableLoading = false;
		},
		UpdateRecords(data) {
			this.pagination.pageNumber = data.page + 1;
			this.getPreBookData(this.selectedDatesForPrebook, true, this.pagination.pageNumber);
		},
		async updateReport(updateParams) {
			this.selectedTab.params = { ...this.selectedTab.params, ...updateParams };
			await this.getPreBookData(null, true, 1);
		},
		async onGetOptimizedReservation(data, callback) {
			// this.tableLoading = true;
			const repricer = useRepricerStore();
			if (data) {
				const params = {
					reservationid: data.reservationId,
					repricerid: data.repricerId
				}
				if (data.reservation.supplier != data.prebook.supplier) {
					params.prebookSupplier = data.prebook.supplier
				}
				const response = await repricer.GetOptimizedReservation(params);

				if (callback && response) {
					callback(response)
				}
			}
		},
		onSetTableLoader(data) {
			this.tableLoading = data
		},
		async initComponent() {
			const auth = useAuthStore();
			this.isSuperAdmin = auth.isSuperAdmin;
			this.userInfo = auth.userInfo;
			this.repricerId = this.userInfo.repricerUserId || this.$route.params.repricerId;
			this.pageType = this.$route.params.type;

			if (!this.repricerId) {
				this.repricerId = this.customerID;
			}
			if (!this.pageType) {
				this.pageType = "active";
			}

			let actionType = this.prebookTypes[this.pageType];
			const selectedItem = this.items.find(item => item.action == actionType);

			if (selectedItem.role.includes('Developer') && !location.search.toLocaleLowerCase().includes('role=developer')) {
				this.pageType = "optimized";
			}
			switch (this.pageType) {
				case "report":
					this.selectedTab = this.reportItem;
					break;

				case "invoice":
					this.selectedTab = this.reportItem;
					break;

				default:
					let actionType = this.prebookTypes[this.pageType];
					this.active = this.items.findIndex(i => i.action == actionType);
					this.selectedTab = this.items[this.active];
					break;
			}

			await this.getPreBookData();
		}
	},
	async mounted() {
		// this.initComponent();
	},
	watch: {
		'$route.params.repricerId': {
			immediate: true,
			handler(newId, oldId) {
				this.initComponent();
			}
		}
	}

}

</script>

<template>
	<main class="d-flex" :class="{'m-3': !isOnlyActive}">
		<Loader v-if="mainLoading" :class="'text-center min-vh-100 w-100'" />
		<div v-else class="w-100">
			<TabMenu v-model:activeIndex="active" :model="roleBasedTabs" class="mb-4 text-nowrap" v-if="!isOnlyActive" />
			<OptimizedNotifications :tableLoading="tableLoading" :preBookResponse="preBookResponse"
				:preBookRequest="prebookRequestParams"
				v-on:onGetOptimizedReservation="(data, callback) => onGetOptimizedReservation(data, callback)"
				v-on:setTableLoader="onSetTableLoader" :notificationType="getNotificationType" :isOnlyActive="isOnlyActive" :customerID="customerID"
				v-on:updateNotifications="getPreBookData">
			</OptimizedNotifications>
			<div class="paginatorwrapper">
				<template v-if="preBookResponse.pagesSummary && preBookResponse.pagesSummary.totalPages > 1">
					<Paginator :rows="preBookResponse.pagesSummary.pageSize" :template="{
						'640px': 'PrevPageLink CurrentPageReport NextPageLink',
						'960px': 'FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink',
						'1300px': 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink',
						default: 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink '
					}" :totalRecords="preBookResponse.pagesSummary.totalRows" @page="UpdateRecords"></Paginator>
				</template>
			</div>
		</div>
	</main>
</template>
