<template>
	<main class="container my-3 d-flex">
		<div class="me-auto ms-auto">
			<Card style="max-width: 30rem;" :pt="{ footer: { class: 'd-flex' } }">
				<template #title>Change password</template>
				<template #content>
					<div class="d-flex flex-column gap-4">
						<Password v-model="params.oldPassword" toggleMask inputClass="w-100" :feedback="false"
							placeholder="Old Password" />
						<Password v-model="params.newPassword" toggleMask inputClass="w-100"
							placeholder="New Password" />
						<Password v-model="params.confirmNewPassword" toggleMask inputClass="w-100" :feedback="false"
							placeholder="Confirm New Password" />

							<div class="d-flex flex-column fz-r0_8 gap-1 text-muted">
								<div>Passwords must have at least one non alphanumeric character.</div>
								<div>Passwords must have at least one lowercase ('a'-'z').</div>
								<div>Passwords must have at least one uppercase ('A'-'Z').</div>
							</div>
					</div>
				</template>
				<template #footer>
					<Button label="Update Password" :disabled="checkFormValidity" class="ms-auto" :loading="loading"
						@click="updatePassword" />
				</template>
			</Card>
			<transition-group name="p-message" tag="div" v-if="error.visible">
				<Message v-for="(message, index) in error.messages" :key="index" severity="error" :life="4000">{{ message }}</Message>
			</transition-group>
			<Message v-if="success.visible" severity="success">{{ success.messages }}</Message>
		</div>
	</main>
</template>

<script>
import { useUserStore } from '@/stores/useUserStore';
import Button from 'primevue/button';
import Card from 'primevue/card';
import Message from 'primevue/message';
import Password from 'primevue/password';

export default {
	name: "Change Password",
	components: { Card, Password, Button, Message },
	data() {
		return {
			params: {},
			loading: false,
			error: {
				visible: false,
				messages: []
			},
			success: {
				visible: false,
				messages: 'Password successfully changed.'
			}
		}
	},
	computed: {
		checkFormValidity() {
			let isInvalid = true;
			if (this.params.oldPassword && this.params.newPassword && this.params.confirmNewPassword && (this.params.newPassword === this.params.confirmNewPassword)) {
				isInvalid = false;
			}
			return isInvalid
		}
	},
	methods: {
		async updatePassword() {
			this.loading = true;
			this.error.visible = false;
			this.success.visible = false;
			const user = useUserStore();
			const response = await user.changePassword(this.params);
			this.loading = false;
			if (response.isError) {
				if (Array.isArray(response.data)) {
					this.error.messages = response.data.map(m => m.description);
					this.error.visible = true;
				} else {
					this.error.messages = [response.data];
					this.error.visible = true;
				}
			} else {
				this.success.visible = true;
				this.params = {}
			}

		}
	},
	async mounted() {
	}
}

</script>
