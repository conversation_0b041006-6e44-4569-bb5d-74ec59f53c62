<template>
    <div class="flex-fill">
        <ConfirmPopup :pt="informationTooltip"></ConfirmPopup>
        <MeterGroup :value="value" v-if="summary && summary.summarizedView" labelPosition="start" :pt="tilesConfig">b
            <template #label="{ value }">
                <div class="flex gap-4 flex-column flex-lg-row">
                    <template v-for="val of value" :key="val.label">
                        <Card class="flex-1">
                            <template #content>
                                <div class="d-flex justify-content-between">
                                    <span class="text-secondary text-sm text-nowrap align-items-center d-flex">
                                        {{ val.label }}
                                        <Button v-if="val.info" @click="showInformation($event, val)"
                                            icon="pi pi-info-circle" class="m-0 p-0" link></Button>
                                    </span>
                                    <div v-if="val.enableFilter" class="align-items-baseline d-flex fw-600 fz-r0_8 gap-2">
                                        <span>{{ invoiceMonth.format('MMMM YYYY') }}</span>
                                        <Calendar style="margin-bottom: -19px;" :pt="calenderConfig" id="invoiceRange"  inputClass="d-none" showIcon  v-on:date-select="invoicChangeMonth" :maxDate="lastDateOfprevMonth"  view="month" dateFormat="MM yy" :manualInput="false" />
                                    </div>
                                </div>
                                <div class="flex flex-column flex-md-row justify-content-md-between">

                                    <div class="d-flex justify-content-between gap-3">
                                        <div class="align-self-center gap-2">
                                            <strong class="fz-r0_7 text-muted d-block" v-if="val.gainLabel" v-html="val.gainLabel"></strong>
                                            <strong class="fz-r0_7 text-muted d-block" v-else>Booking <br> Amount:</strong>
                                            <div class="fs-4 text-nowrap">{{ val.gain }}</div>
                                        </div>
                                        <Knob v-if="val.gainPercent" :pt="progressConfig(val)" v-model="val.gainPercent"
                                            readonly :min="0" :max="100" class="align-self-end" valueTemplate="{value}%"
                                            :strokeWidth="5" />
                                        
                                    </div>
                                    <Divider class="d-md-none" />
                                    <div class="d-flex justify-content-between gap-3">
                                        <div class="align-self-center gap-2">
                                            <strong class="fz-r0_7 text-muted d-block" v-if="val.countLabel" v-html="val.countLabel"></strong>
                                            <strong class="fz-r0_7 text-muted d-block" v-else>Booking <br> Count:</strong>
                                            <div class="fs-4 text-nowrap">{{ val.count }}</div>
                                        </div>
                                        <Knob :pt="progressConfig(val)" v-if="val.countPercent"
                                            v-model="val.countPercent" readonly :min="0" :max="100"
                                            class="align-self-end" valueTemplate="{value}%" :strokeWidth="5" />
                                        <Knob :pt="progressConfig(val)" v-else style="visibility: hidden; width: 1px;"
                                            v-model="val.countPercent" readonly :min="0" :max="100"
                                            class="align-self-end d-none d-md-block" valueTemplate="{value}%"
                                            :strokeWidth="5" />
                                    </div>
                                </div>
                            </template>
                            <template #footer>
                                <div class="align-items-center d-flex fz-r0_8 text-muted">
                                    <div>Average <strong>{{ val.avg }}</strong> per reservation.</div>
                                    <Button icon="pi pi-external-link" @click="redirectTo(val.redirectTo)" class="ms-auto" :label="'Show more'" size="small" aria-label="Submit" link />
                                </div>
                            </template>
                        </Card>
                    </template>

                </div>
            </template>
        </MeterGroup>
        <MeterGroup :value="value" v-else labelPosition="start" :pt="tilesConfig">
            <template #label="{ value }">
                <div class="flex flex-wrap gap-3 flex-column flex-lg-row">
                    <template v-for="val of value" :key="val.label">
                        <Card class="flex-1">
                            <template #content>
                                <span class="text-secondary text-sm text-nowrap align-items-center d-flex">
                                    {{ val.label }}
                                </span>
                                <div class="flex flex-column flex-md-row justify-content-md-between">
                                    <div class="d-flex justify-content-between gap-3">
                                        <div class="align-self-center gap-2">
                                            <strong class="fz-r0_7 text-muted d-block">Booking <br> Amount:</strong>
                                            <Skeleton width="7rem" height="2rem" class="mt-1"></Skeleton>
                                            <Skeleton width="3rem" height="1rem" class="mt-2"></Skeleton>
                                        </div>
                                        <Skeleton v-if="val.countPercent != undefined" shape="circle" size="6rem">
                                        </Skeleton>
                                        <Skeleton v-else shape="circle" size="6rem" style="visibility: hidden;">
                                        </Skeleton>
                                    </div>
                                    <Divider class="d-md-none" />
                                    <div class="d-flex justify-content-between gap-3">
                                        <div class="align-self-center gap-2">
                                            <strong class="fz-r0_7 text-muted d-block">Booking <br> Count:</strong>
                                            <Skeleton width="4rem" height="2rem" class="mt-1"></Skeleton>
                                            <Skeleton width="2rem" height="1rem" class="mt-2"></Skeleton>
                                        </div>
                                        <Skeleton v-if="val.countPercent != undefined" shape="circle" size="6rem">
                                        </Skeleton>
                                        <Skeleton v-else shape="circle" size="6rem" style="visibility: hidden;">
                                        </Skeleton>
                                    </div>
                                </div>
                            </template>
                            <template #footer>
                                <div class="text-muted fz-r0_8">
                                    <Skeleton width="12rem" height="1rem" class="mt-1"></Skeleton>
                                </div>
                            </template>
                        </Card>
                    </template>
                </div>
            </template>
        </MeterGroup>
    </div>
</template>
<script>
import moment from 'moment';
import Button from 'primevue/button';
import Card from 'primevue/card';
import MeterGroup from 'primevue/metergroup';
import Tag from 'primevue/tag';
import ConfirmPopup from 'primevue/confirmpopup';
import ProgressBar from 'primevue/progressbar';
import Knob from 'primevue/knob';
import Divider from 'primevue/divider';
import Menu from 'primevue/menu';
import Skeleton from 'primevue/skeleton';
import Timeline from 'primevue/timeline';
import Calendar from 'primevue/calendar';

import { useRepricerStore } from '@/stores/useRepricerStore';
import { useAuthStore } from '@/stores/useAuthStore';




const documentStyle = getComputedStyle(document.documentElement);

export default {
    name: "SummaryTiles",
    components: { MeterGroup, Card, Timeline, Button, Tag, ConfirmPopup, Menu, ProgressBar, Knob, Divider, Skeleton, Calendar },
    data() {
        return {
            isSuperAdmin: false,
            isRoleAdmin: false,
            tilesConfig: {
                metercontainer: {
                    class: 'd-none'
                }
            },
            calenderConfig: {
                rootdropdownbutton: {
                    class: 'rounded-3'
                }
            },
            informationTooltip: {
                footer: {
                    class: "d-none"
                },
                content: {
                    class: "fz-r0_9"
                },
                icon: {
                    class: "fz-r0_9"
                },
                root: {
                    style: 'max-width:350px'
                }
            },
            selectedPotential: 'TOTAL',
            invoiceInfo: null,
            lastDateOfprevMonth: null,
            invoiceMonth: new Date(moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'))
        };
    },
    props: ['summary', 'redirectTo'],
    watch: {},
    computed: {
        value() {
            let data = []
            const defaultData = [
                {
                    label: 'Currently Available Optimizations',
                    color: documentStyle.getPropertyValue('--blue-500'),
                    info: "It encompasses all the reservations where rePricer has found a lower price for a specific hotel reservation and Cancellation policy edge cases. Still, the cancellation policy is within your configuration. ",
                    enableFilter: false,

                    avg: this.$filters.priceDisplay(0, 'EUR', true),

                    gain: this.$filters.priceDisplay(0, 'EUR', true),
                    totalGain: this.$filters.priceDisplay(0, 'EUR', true),
                    gainPercent: 0,
                    count: 0,
                    totalCount: 0,
                    countPercent: 0,

                    redirectTo: '/optimizations/active' 
                },
                {
                    label: 'Realized Optimizations',
                    color: documentStyle.getPropertyValue('--green-500'),
                    info: `The realized optimization is the difference between the original booking price and the new, lower price secured through rebooking.`,
                    enableFilter: false,

                    avg: this.$filters.priceDisplay(0, 'EUR', true),

                    gain: this.$filters.priceDisplay(0, 'EUR', true),
                    count: 0,

                    redirectTo: '/optimizations/optimized'
                },
            ];
            if (this.isSuperAdmin || this.isRoleAdmin) {
                defaultData.push({
                    label: 'Invoice',
                    color: documentStyle.getPropertyValue('--green-500'),
                    info: ``,
                    enableFilter: true,

                    avg: this.$filters.priceDisplay(0, 'EUR', true),

                    gain: this.$filters.priceDisplay(0, 'EUR', true),
                    count: 0,

                    gainLabel: 'Realized <br> Gain:',
                    countLabel: 'Invoiceable <br> Amount:',

                    redirectTo: '/invoice'
                })
            }
            let total = null;
            if (this.summary && this.summary.summarizedView && this.summary.summarizedView.length > 0 && 'TOTAL') {
                total = this.summary.summarizedView.find(s => s.reportType.toLowerCase() == 'total');
            }
            if (total) {
                total.profit = total.profit || 0;
                let totalInvoice = 0;
                let totalInvoiceCount = 0;
                let totalInvoiceableAmount = 0;
                if (this.invoiceInfo) {
                    totalInvoice = this.invoiceInfo.reservationReports.reduce((a,c) => a + c.optimizationProfit, 0);
                    totalInvoiceableAmount = this.invoiceInfo.reservationReports.reduce((a,c) => a + c.optimizationProfit / 2, 0);
                    totalInvoiceCount = this.invoiceInfo.reservationReports.length;
                }
                data = [
                    {
                        ...defaultData[0],
                        avg: this.$filters.priceDisplay(total.reservationsCount ? (total.profit / total.reservationsCount) : 0, this.currency, true),

                        gain: this.$filters.priceDisplay(total.profit, this.currency, true),
                        // totalGain: this.$filters.priceDisplay(total.reservationPrice, this.currency, true),
                        gainPercent: parseFloat(((total.profit / total.reservationPrice) * 100).toFixed(2)),

                        count: total.reservationsCount,
                        // totalCount: this.summary.filteredReservationCount,
                        countPercent: parseFloat(((total.reservationsCount / this.summary.filteredReservationCount) * 100).toFixed(2)),
                    },
                    {
                        ...defaultData[1],
                        avg: this.$filters.priceDisplay(this.summary.realizedGainCount ? (this.summary.realizedGain / this.summary.realizedGainCount) : 0, this.currency, true),

                        gain: this.$filters.priceDisplay(this.summary.realizedGain, this.currency, true),

                        count: this.summary.realizedGainCount,
                    },
                ];

                if (this.isSuperAdmin || this.isRoleAdmin) {
                    data.push({
                        ...defaultData[2],
                        avg: this.$filters.priceDisplay(totalInvoiceCount ? (totalInvoice / totalInvoiceCount) : 0, this.currency, true),

                        gain: this.$filters.priceDisplay(totalInvoice, this.currency, true),

                        count: this.$filters.priceDisplay(totalInvoiceableAmount, this.currency, true),
                    })
                }

            } else {
                data = defaultData
            }
            return data;
        },
        currency() {
            return this.summary.summarizedView[0] ? this.summary.summarizedView[0].currency : 'EUR'
        },
        totalInvoce() {
            if (this.invoiceInfo) {
                return this.invoiceInfo.reservationReports.reduce((a,c) => a + c.optimizationProfit, 0)
            }

            return 0
        }
    },
    async mounted() {
        this.getInvoiceInfo();
    },
    methods: {
        invoicChangeMonth(date) {
            this.getInvoiceInfo(date);
        },
        async getInvoiceInfo(selectedMonth) {
            this.lastDateOfprevMonth = new Date(moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'));
            const auth = useAuthStore();
            const userInfo = auth.userInfo;

            this.isSuperAdmin = auth.isSuperAdmin;
            this.isRoleAdmin = auth.userInfo.roleName.find(role => role.toLowerCase() == "admin" );
            const repricerInfo = useRepricerStore();

            const givenDate = selectedMonth ? moment(new Date(selectedMonth)) : moment(new Date());
            let params = {
                "repricerId": userInfo.repricerUserId || this.$route.params.repricerId,
                "fromDate": moment(givenDate).startOf('month').format('YYYY-MM-DD')
            }
            if (givenDate.isSame(moment(), 'month')) {
                params["toDate"] = moment().format('YYYY-MM-DD');
            } else {
                params["toDate"] = moment(givenDate).endOf('month').format('YYYY-MM-DD');
            }

            
            let currentDate = moment(selectedMonth) || moment();
            this.invoiceMonth = currentDate.clone().subtract(selectedMonth ? 0 : 1, 'month').startOf('month');
            params["fromDate"] = currentDate.clone().subtract(selectedMonth ? 0 : 1, 'month').startOf('month').format('YYYY-MM-DD');
            params["toDate"] = currentDate.clone().subtract(selectedMonth ? 0 : 1, 'month').endOf('month').format('YYYY-MM-DD');


            this.invoiceInfo = await repricerInfo.GetInvoiceReport(params);
        },
        showInformation(event, value) {
            this.$confirm.require({
                target: event.currentTarget,
                message: value.info,
                icon: 'pi pi-info-circle',
                rejectClass: 'd-none',
                acceptClass: 'd-none'
            });
        },
        progressConfig(value) {
            return {
                value: {
                    stroke: value.color
                },
                label: {
                    class: "fz-r1",
                    fill: value.color
                }
            }
        }
    }
}

</script>

<style>
div#invoiceRange_panel {
    min-width: 250px;
}
span#invoiceRange .p-datepicker-trigger {
    border-radius: 0;
    padding: 0;
    width: 100%;
    background: transparent;
    color: #000;
    border: 0
}
</style>