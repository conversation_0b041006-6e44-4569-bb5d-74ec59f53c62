<template>
    <form class="d-flex flex-column gap-4 w-100" @submit.prevent="onsubmit">
        <div class="d-flex flex-wrap row">
            <div class="d-flex flex-column col-12 col-lg-4 align-self-center">
                <label class="text-muted mb-1" for="repricerUserName">Name: <span class="fw-bold text-primary"
                        v-if="createRepricerPayload.repricerUserID != 0">{{ createRepricerPayload.repricerUserName
                        }}</span></label>
                <InputText type="text" v-if="isSuperAdmin && !isEdit"
                    v-model="createRepricerPayload.repricerUserName" />
                <InputText type="hidden" v-else v-model="createRepricerPayload.repricerUserName" />
            </div>
            <div class="d-flex flex-column col-12 col-lg-4"
                v-if="createRepricerPayload.extraClientDetail && isSuperAdmin">
                <label class="text-muted mb-1" for="reportEmailToSend">Report Email To Send</label>
                <InputText type="text" v-model="createRepricerPayload.extraClientDetail.reportEmailToSend" required />
            </div>
        </div>

        <Fieldset legend="IRIX Account" v-if="isSuperAdmin">
            <div class="d-flex flex-wrap row">
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="adminUrl">Admin URL</label>
                    <InputText type="text" v-model="createRepricerPayload.adminUrl" required />
                </div>
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="adminUserId">Admin API Key</label>
                    <InputText type="text" v-model="createRepricerPayload.adminUserId" required />
                </div>
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="adminPassword">Admin Secret Key</label>
                    <InputText type="text" v-model="createRepricerPayload.adminPassword" required />
                </div>
            </div>
            <div class="d-flex flex-wrap row">
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="resellerUrl">Reseller URL</label>
                    <InputText type="text" v-model="createRepricerPayload.resellerUrl" required />
                </div>
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="resellerUserId">Reseller API Key</label>
                    <InputText type="text" v-model="createRepricerPayload.resellerUserId" required />
                </div>
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="resellerPassword">Reseller Secret Key</label>
                    <InputText type="text" v-model="createRepricerPayload.resellerPassword" required />
                </div>
            </div>
        </Fieldset>


        <Fieldset legend="Optimization Criteria">
            <div class="d-flex flex-wrap row" v-if="createRepricerPayload.extraClientDetail">
                <div class="d-flex flex-column col-12 col-lg-4 mb-3 d-none">
                    <label class="text-muted mb-1" for="travelDaysMaxSearchInDays">Earliest Date Before Check-In</label>
                    <InputNumber inputId="travelDaysMaxSearchInDays"
                        v-model="createRepricerPayload.extraClientDetail.travelDaysMaxSearchInDays" required
                        :min="createRepricerPayload.extraClientDetail.travelDaysMinSearchInDays + 1" />
                </div>
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="travelDaysMinSearchInDays">Days Before Cancellation Policy
                        Start</label>
                    <InputNumber inputId="travelDaysMinSearchInDays"
                        v-model="createRepricerPayload.extraClientDetail.travelDaysMinSearchInDays" required :min="1"
                        :max="createRepricerPayload.extraClientDetail.travelDaysMaxSearchInDays - 1" />
                </div>
                <div class="d-flex flex-column col-12 col-lg-4 mb-3 d-none">
                    <label class="text-muted mb-1" for="clientConfig_DaysDifferenceInPreBookCreation">Optimization
                        Frequency</label>
                    <InputNumber readonly="" inputId="clientConfig_DaysDifferenceInPreBookCreation"
                        v-model="createRepricerPayload.extraClientDetail.clientConfig_DaysDifferenceInPreBookCreation"
                        :min="1" required input-class="bg-gray-100" />
                </div>
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="daysLimitCancellationPolicyEdgeCase">Days Limit Cancellation
                        Policy Difference</label>
                    <InputNumber inputId="daysLimitCancellationPolicyEdgeCase"
                        v-model="createRepricerPayload.extraClientDetail.daysLimitCancellationPolicyEdgeCase" :min="1"
                        required />
                </div>
            </div>
            <div class="d-flex flex-wrap row" v-if="isSuperAdmin && createRepricerPayload.extraClientDetail">
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="priceDifferenceValue">Gain Threshold Price</label>
                    <InputNumber inputId="priceDifferenceValue" inputClass="text-start" :min="1"
                        v-model="createRepricerPayload.extraClientDetail.priceDifferenceValue" required
                        :disabled="createRepricerPayload.extraClientDetail.isUsePercentage"
                        :variant="createRepricerPayload.extraClientDetail.isUsePercentage ? 'filled' : ''" />
                </div>
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="priceDifferenceValue">Gain Threshold Currency</label>
                    <Dropdown v-model="createRepricerPayload.extraClientDetail.currency" :options="currenciesDropDown"
                        optionLabel="name" optionValue="code" placeholder="Select Currency" />
                </div>
            </div>
            <template v-else>
                <div class="d-flex flex-wrap row" v-if="createRepricerPayload.extraClientDetail">
                    <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                        <label class="text-muted mb-1" for="priceDifferenceValue">Gain Threshold</label>
                        <InputNumber inputId="priceDifferenceValue"
                            :suffix="' ' + createRepricerPayload.extraClientDetail.currency" inputClass="text-start"
                            :min="1" v-model="createRepricerPayload.extraClientDetail.priceDifferenceValue" required
                            :disabled="createRepricerPayload.extraClientDetail.isUsePercentage"
                            :variant="createRepricerPayload.extraClientDetail.isUsePercentage ? 'filled' : ''" />
                    </div>
                </div>
            </template>
        </Fieldset>

        <Fieldset legend="CRON Jobs" v-if="isSuperAdmin">
            <div class="d-flex flex-wrap row" v-if="createRepricerPayload.clientConfigScheduler">
                <div class="d-flex flex-column col-12 col-lg-3 mb-3">
                    <label class="text-muted mb-1" for="preBook_CronTime">Cron Job Time</label>
                    <InputText type="text" v-model="createRepricerPayload.clientConfigScheduler.preBook_CronTime"
                        required />
                    <CronParser :cronExpr="createRepricerPayload.clientConfigScheduler.preBook_CronTime"
                        v-on:onComplete="v => preBook_CronTime_valid = v" />
                </div>
                <div class="d-flex flex-column col-12 col-lg-3 mb-3">
                    <label class="text-muted mb-1" for="timeZoneId">Time zone</label>
                    <Dropdown v-model="createRepricerPayload.clientConfigScheduler.timeZoneId"
                        :options="sortedTimeZones" optionLabel="timeZoneName" optionValue="timeZoneId"
                        placeholder="Select Timezone" />
                </div>
                <div class="d-flex flex-column col-12 col-lg-3 mb-3">
                    <label class="text-muted mb-1" for="optimizationType">Optimization Type</label>
                    <Dropdown v-model="createRepricerPayload.optimizationType" :options="optimizationTypes"
                        optionLabel="name" optionValue="code" placeholder="Select Optimization Type" />
                </div>
                <div class="d-flex flex-column col-12 col-lg-3 mb-3">
                    <label class="text-muted mb-1" for="isJobsEnable">Optimization Job Status</label>
                    <Dropdown v-model="createRepricerPayload.isJobsEnable" :options="optimizationJobStatus"
                        optionLabel="name" optionValue="code" placeholder="Select Optimization Job Status" />
                </div>
            </div>
        </Fieldset>

        <Fieldset legend="Admin User Details" v-if="isSuperAdmin && createUserForm">
            <div class="d-flex flex-wrap row">
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="Password">Password</label>
                    <Password id="password" pt:input:class="w-100" inputClass="w-100" class="flex-auto w-100"
                        autocomplete="off" v-model="createUserForm.password" />
                </div>
                <div class="d-flex flex-column col-12 col-lg-4 mb-3">
                    <label class="text-muted mb-1" for="ConfirmPassword">Confirm Password</label>
                    <Password id="password" pt:input:class="w-100" inputClass="w-100" class="flex-auto w-100"
                        autocomplete="off" v-model="createUserForm.confirmPassword" />
                </div>
            </div>
            <ul class="fz-r0_8 m-0 mb-3 ps-4">
                <li>Password must be at least 8 characters.</li>
                <li>Password must contain at least one lowercase letter.</li>
                <li>Password must contain at least one uppercase letter.</li>
                <li>Password must contain at least one special character.</li>
            </ul>
        </Fieldset>

        <Fieldset legend="Restrictions">
            <Restrictions :createRepricerPayload="createRepricerPayload" />
        </Fieldset>

        <div class="d-flex flex-wrap justify-content-end gap-3">
            <Button type="button" icon="pi pi-times" label="Clear" severity="secondary" :loading="isProgress"
                v-if="isSuperAdmin" @click="onClear" />
            <Button type="submit" icon="pi pi-check" :label="isEdit ? 'Update Configurations' : 'Create'"
                :loading="isProgress"
                :disabled="!preBook_CronTime_valid || !matched_CronTime_valid || createUserForm?.password != createUserForm?.confirmPassword" />
        </div>
    </form>
    <ConfirmDialog group="headless">
        <template #container="{ message, acceptCallback, rejectCallback }">
            <div class="flex flex-column align-items-center p-5 surface-overlay border-round">
                <div
                    class="border-circle bg-primary inline-flex justify-content-center align-items-center h-6rem w-6rem -mt-8">
                    <i class="pi pi-question text-5xl"></i>
                </div>
                <span class="font-bold text-2xl block mb-2 mt-4">{{ message.header }}</span>
                <p class="mb-0" v-html="message.message"></p>
                <div class="flex align-items-center gap-2 mt-4">
                    <Button label="Save" @click="acceptCallback" class="w-8rem"></Button>
                    <Button label="Cancel" outlined @click="rejectCallback" class="w-8rem"></Button>
                </div>
            </div>
        </template>
    </ConfirmDialog>
</template>

<script>
import Card from 'primevue/card';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';
import Fieldset from 'primevue/fieldset';
import InputNumber from 'primevue/inputnumber';
import InputSwitch from 'primevue/inputswitch';
import InputGroup from 'primevue/inputgroup';
import InputGroupAddon from 'primevue/inputgroupaddon';
import { useAuthStore } from '@/stores/useAuthStore';
import CronParser from '@/components/repricer/CronParser.vue';
import { useRepricerStore } from '@/stores/useRepricerStore';
import Dropdown from 'primevue/dropdown';
import { matchedCronTime } from '@/helpers/utils';
import Password from 'primevue/password';
import ConfirmDialog from 'primevue/confirmdialog';
import Restrictions from './Restrictions.vue';



export default {
    name: "Manage Repricer",
    components: { Card, InputText, Button, InputSwitch, Fieldset, InputNumber, InputGroup, InputGroupAddon, CronParser, Dropdown, Password, ConfirmDialog, Restrictions },
    props: ['createRepricerPayload', 'activeCurrencies', 'onsubmit', 'isProgress', 'onClear', 'isEdit', 'createUserForm'],
    data() {
        return {
            isSuperAdmin: false,
            preBook_CronTime_valid: true,
            timezones: null,
            selectedTiemZone: '',
            optimizationTypes: [
                // { name: 'Demo', code: 1 },
                { name: 'Semi-Automation', code: 2 },
                { name: 'Automation', code: 3 },
            ],
            optimizationJobStatus: [
                // { name: 'Demo', code: 1 },
                { name: 'Enable', code: true },
                { name: 'Disable', code: false },
            ]
        }
    },
    computed: {
        currenciesDropDown() {
            let options = [];
            this.activeCurrencies?.map(c => options.push({ name: c, code: c }));
            if (options.length == 0) {
                options = [{ name: "EUR", code: "EUR" }]
            }
            return options
        },
        sortedTimeZones() {
            return this.timezones?.sort((a, b) => a.timeZoneId - b.timeZoneId);
        },
        matched_CronTime_valid() {
            let isValid = true;

            if (this.createRepricerPayload?.clientConfigScheduler?.preBook_CronTime) {
                isValid = !matchedCronTime(this.createRepricerPayload.clientConfigScheduler.preBook_CronTime)
            }

            return isValid
        }
    },
    methods: {
        onCRONComplete(data) {
            console.log(data)
        },
        async getTimeZones() {
            const repricer = useRepricerStore();
            this.timezones = await repricer.GetTimeZones();
        }
    },
    async mounted() {
        const auth = useAuthStore();
        this.isSuperAdmin = auth.isSuperAdmin;
        this.getTimeZones();
    }
}

</script>