<template>
  <main class="container my-3">
    <Card>
      <template #title>Create Customer</template>
      <template #content>
        <ManageRepricer :createRepricerPayload="createRepricerPayload" :createUserForm="createUserForm"
          :onsubmit="createRepricer" :onClear="initRepricerForm" :isProgress="isProgress" />
      </template>
    </Card>
  </main>
</template>

<script>
import { showErrorMessage } from '@/helpers/utils';
import { useRepricerStore } from '@/stores/useRepricerStore';
import { useUserStore } from '@/stores/useUserStore';
import Card from 'primevue/card';
import ManageRepricer from '@/components/repricer/ManageRepricer.vue';

const createPayload = {
  "repricerUserID": 0,                        // 0 in case of create
  "repricerUserName": "",
  "adminUrl": "",
  "adminUserId": "",
  "adminPassword": "",
  "resellerUrl": "",
  "resellerUserId": "",
  "resellerPassword": "",
  "adminApiScope": "read:reservations",       // Hard coded for now, can be dynamic in future
  "resellerApiScope": "read:hotels-search",   // Hard coded for now, can be dynamic in future
  "isActive": true,                           // Hard coded for now
  "rePricerDetail": "",
  "clientConfiguration": {
    "service": "hotel",                       // Hard coded for now
    "cancelPenalty": false                    // Hard coded for now
  },
  "extraClientDetail": {
    "travelDaysMaxSearchInDays": 60,
    "travelDaysMinSearchInDays": 10,
    // "maxNumberOfTimesOptimization": 5,     // removed this configuuration
    "clientConfig_DaysDifferenceInPreBookCreation": 1,
    "priceDifferenceValue": 20,
    "priceDifferencePercentage": 0,
    "isUsePercentage": false,
    "reportEmailToSend": "",
    "daysLimitCancellationPolicyEdgeCase": 10,
    "isUseDaysLimitCancellationPolicyEdgeCase": true,
    "isCreatePrebookForPriceEdgeCase": false
  },
  "clientConfigScheduler": {
    "reservation_CronTime": "10 */2 * * *",
    "preBook_CronTime": "5 */5 * * *",
    "currencyExchange_CronTime": "5 */5 * * *"
  },
  "optimizationType": 2
}

export default {
  name: "Create Repricer",
  components: { Card, ManageRepricer },
  data() {
    return {
      createRepricerPayload: {},
      extraClientDetail: {},
      clientConfigScheduler: {},
      isLoading: true,
      isProgress: false,
      createUserForm: {
        password: "",
        confirmPassword: "",
      }
    }
  },
  computed: {
  },
  methods: {
    initRepricerForm() {
      this.createRepricerPayload = JSON.parse(JSON.stringify(createPayload));
    },
    async createRepricer() {
      this.isProgress = true;
      const repricer = useRepricerStore();
      const user = useUserStore();

      const payload = this.createRepricerPayload;
      const passwordValidation = this.$filters.validatePassword(this.createUserForm.password);
      if (!passwordValidation.status || !payload.extraClientDetail.currency || !payload.clientConfigScheduler.timeZoneId) {
        showErrorMessage(this.$toast, { data: 'Something went wrong' }, passwordValidation.errorMessage || "Check all required fields.")
      } else {
        const response = await repricer.createRepricer(payload);

        if (!response || response.isError || response.errors) {
          showErrorMessage(this.$toast, response || { data: 'Something went wrong' }, 'Not able to create customer.')
        } else {
          this.createUserForm = {
            ...this.createUserForm,
            userName: response.data.extraClientDetail.reportEmailToSend,
            email: response.data.extraClientDetail.reportEmailToSend,
            roles: ["Admin"],
            repricerId: response.data.repricerUserID
          };
          if (this.createUserForm.password != "" && this.createUserForm.confirmPassword != "") {
            await user.createUser(this.createUserForm);
          }
          this.initRepricerForm();
        }
      }
      this.isProgress = false;
    }
  },
  async mounted() {
    this.initRepricerForm();
    this.isLoading = false;
  }
}

</script>
