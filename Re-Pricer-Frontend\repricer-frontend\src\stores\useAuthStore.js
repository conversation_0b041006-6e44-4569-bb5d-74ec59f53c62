import { defineStore } from 'pinia'
import { useApiStore } from './useApiStore';
import { getCookie, deleteCookie, decodeJWT } from '@/helpers/utils';
import constants from '@/helpers/constants';
import moment from 'moment';
import { useRepricerStore } from './useRepricerStore';
import { useUserStore } from '@/stores/useUserStore';

export const useAuthStore = defineStore({
	// Set persist to true
	persist: true,
	id: "auth",
	state: () => ({
		userInfo: {},
		rePricerInfo: {},
		isSuperAdmin: false,
		isLoggedin: false,
		checkToken: null
	}),
	actions: {
		checkTokenExpiration() {
			const token = getCookie(constants.tokenkey);
			if (token) {
				const tokenData = JSON.parse(atob(token.split('.')[1]));
				const expirationTime = tokenData.exp * 1000; // Convert expiration time to milliseconds
				const currentTime = Date.now();

				const diff = expirationTime - currentTime;
				const expireTime = moment(new Date(expirationTime)).format('HH:mm:ss');

				if (diff <= 0) {
					console.log(`Your session was expired. Expire time is ${expireTime}`)
					return "expired";
				}
				if (diff < (constants.alertForTokenExpire)) {
					console.log(`Your session is going to expire. do refresh token. Expire time is ${expireTime}`)
					return "alert";
				}
				console.log(`Your session is active. Expire time is ${expireTime}`)
				return "loggedin";
			}
			return false;
		},
		async login(loginData) {
			const api = useApiStore();
			const repricer = useRepricerStore();

			await this.logout();
			const response = await api.post("/token", loginData, true);
			if (response?.access_token) {
				if (response.repricerUserId != 0)
					await repricer.getRePricerById(response.repricerUserId);
				this.isLoggedin = true;

				const userData = JSON.parse(JSON.stringify(response));
				const userInfo = decodeJWT(response.access_token);
				delete userData['access_token'];
				delete userData['expires'];
				userData['id'] = userInfo[Object.keys(userInfo).find(k => k.indexOf('nameidentifier') >= 0)];
				this.isSuperAdmin = userData.roleName.indexOf('SuperAdmin') >= 0;
				// if (this.isSuperAdmin) {
				// 	userData.roleName.push('SuperAdmin')
				// }
				this.userInfo = userData;
			}
			return response;
		},
		async refreshToken() {
			const api = useApiStore();
			const token = getCookie(constants.tokenkey);
			const response = await api.post("/refresh-token", {
				refreshToken: token
			}, true);
			return response;
		},
		async logout() {
			const user = useUserStore();
			user.users = [];
			this.userInfo = {};
			deleteCookie(constants.tokenkey);
			this.isLoggedin = false;
			this.isSuperAdmin = false;
			localStorage.clear();
		},
		AddDeveloperMode() {
			if (!this.userInfo.roleName.includes('Developer'))
				this.userInfo.roleName.push('Developer')
		},
		removeDeveloperMode() {
			if (this.userInfo.roleName.includes('Developer'))
				this.userInfo.roleName = this.userInfo.roleName.filter(fruit => fruit !== "Developer");
		}
	}
	// ... The rest of the code
});