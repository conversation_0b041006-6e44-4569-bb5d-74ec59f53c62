<script>
import { useAuthStore } from '@/stores/useAuthStore';
import { useRepricerStore } from '@/stores/useRepricerStore';
import Button from 'primevue/button';
import Panel from 'primevue/panel';
import Chip from 'primevue/chip';
import ProgressBar from 'primevue/progressbar';
import ProgressSpinner from 'primevue/progressspinner';
import CronParser from '@/components/repricer/CronParser.vue';
import Tag from 'primevue/tag';


export default {
    name: "Configuration",
    components: {
        Panel, Button, Chip, ProgressBar, ProgressSpinner, CronParser, Tag
    },
    data() {
        return {
            panelPT: {
                title: {
                    class: "text-muted text-capitalize"
                }
            },
            showEditConfigButton: false,
            optimizationTypes: {
                "0": "Demo",
                "1": "Demo",
                "2": "Semi-Automation",
                "3": "Automation",
            },
            irixConfig: null,
            irixConfigLoading: true,
            timezones: null,
            auth: null,
            config: null
        }
    },
    computed: {
        selectedTimeZone() {
            let selectedTimeZone = this.config.clientConfigScheduler.timeZoneId;
            if (this.timezones) {
                const filtered = this.timezones.find(tz => tz.timeZoneId == selectedTimeZone);
                if (filtered) {
                    selectedTimeZone = filtered.timeZoneName
                }
            }
            return selectedTimeZone
        }
    },
    methods: {
        goToUpdate() {
            if (this.$route.params.repricerId) {
                this.$router.push({path: '/repricer/update/' + this.$route.params.repricerId})
            } else {
                this.$router.push({path: '/repricer/update'})
            }
        },
        goToRestrictions() {
            if (this.$route.params.repricerId) {
                this.$router.push({path: '/repricer/restrictions/' + this.$route.params.repricerId})
            } else {
                this.$router.push({path: `/repricer/restrictions/${this.auth.userInfo.repricerUserId}`})
            }
        },
        async getTimeZones() {
            this.irixConfig = null;
            const repricer = useRepricerStore();
            this.timezones = await repricer.GetTimeZones();
        },
        async getIrixConfiguration () {
            const rePricer = useRepricerStore();
            let repricerUserID = rePricer.repricer.repricerUserID;

            if (this.$route.params.repricerId) {
                repricerUserID = this.$route.params.repricerId
            }
            this.irixConfig = await rePricer.GetIrixConfiguration(repricerUserID);
            this.irixConfigLoading = false;
        },
        async getRepricerConfig() {
            const rePricer = useRepricerStore();
            const repricerID = this.$route.params.repricerId || rePricer.repricer.repricerUserID;
            this.config = await rePricer.getRePricerById(repricerID);
        },
        async initComponent() {
            this.getRepricerConfig();
            this.auth = useAuthStore();
            if (this.auth.isSuperAdmin || this.auth.userInfo.roleName.indexOf('Admin') >=0) {
                this.showEditConfigButton = true;
            }
            this.getIrixConfiguration();
            this.getTimeZones();
        }
    },
    async mounted() {
        // this.initComponent();
    },
    watch: {
		'$route.params.repricerId': {
			immediate: true,
			handler(newId, oldId) {
				this.initComponent();
			}
		}
	}
}

</script>
auth.
<template>
    <main class="container my-3 min-vh-100">
        <div class="d-flex flex-column flex-sm-row justify-content-between">
            <h2 class="px-2">Customer configurations</h2>
            <div class="align-items-center flex">
                <Button v-if="showEditConfigButton" link class="ms-auto" label="Edit Configurations" @click="goToUpdate()" />
                <Button v-if="showEditConfigButton" link class="ms-auto" label="Edit Restrictions" @click="goToRestrictions()" />
            </div>
        </div>
        <template v-if="config">
            <div class="d-flex flex-wrap gap-3 gap-lg-0 row">
                <div class="col-12 col-lg-4 mb-lg-4 d-none">
                    <Panel header="Optimization frequency" :pt="panelPT" class="bg-gray-100 w-100">
                        <div class="fs-2" v-if="config.extraClientDetail.clientConfig_DaysDifferenceInPreBookCreation < 2">
                            {{ config.extraClientDetail.clientConfig_DaysDifferenceInPreBookCreation }} Day
                        </div>
                        <div class="fs-2" v-else>
                            {{ config.extraClientDetail.clientConfig_DaysDifferenceInPreBookCreation }} Days
                        </div>
                    </Panel>
                </div>
                <div class="col-12 col-lg-4 mb-lg-4 d-flex">
                    <Panel header="Minimum gain from offer" :pt="panelPT" class="w-100">
                        <div class="fs-2">
                            <template v-if="config.extraClientDetail.isUsePercentage">
                                {{ config.extraClientDetail.priceDifferencePercentage }}%
                            </template>
                            <template v-else>
                                {{ config.extraClientDetail.priceDifferenceValue }} {{config.extraClientDetail.currency}}
                            </template>
                        </div>
                        <template #footer>
                            <small class="text-muted">The minimum price gain from the optimizer offer.</small>
                        </template>
                    </Panel>
                </div>
                <div class="col-12 col-lg-4 mb-lg-4 d-flex">
                    <Panel header="Optimize non-refundable reservations?" class="w-100" :pt="panelPT">
                        <div class="fs-2">
                            No
                        </div>
                        <template #footer>
                            <small class="text-muted">Allow to optimize reservations that have active policy with penalty.</small>
                        </template>
                    </Panel>
                </div>
            
                <div class="col-12 col-lg-4 mb-lg-4 d-flex">
                    <Panel header="Days Limit Cancellation Policy Difference" :pt="panelPT" class="w-100">
                        <div class="fs-2 text-capitalize">
                            <div>{{ config.extraClientDetail.daysLimitCancellationPolicyEdgeCase }}</div>
                        </div>
                        <template #footer>
                            <small class="text-muted">The maximum number of days allowed between the cancellation policies of the original reservation and the optimized reservation.</small>
                        </template>
                    </Panel>
                </div>
            </div>
            <div class="d-flex flex-wrap gap-3 gap-lg-0 row">
                <div class="col-12 col-lg-4 mb-lg-4">
                    <Panel header="Days before cancellation policy start" :pt="panelPT" class="h-100">
                        <div class="fs-2 text-capitalize">
                            <div>{{ config.extraClientDetail.travelDaysMinSearchInDays }}</div>
                        </div>
                        <template #footer>
                            <small class="text-muted">Offset in days before the first applicable cancellation policy. If the policy is applicable before, the reservation is consider non-refundable.</small>
                        </template>
                    </Panel>
                </div>
                <div class="col-12 col-lg-4 mb-lg-4">
                    <Panel header="Optimization Type" :pt="panelPT" class="h-100">
                        <div class="fs-2 text-capitalize">
                            <div>{{ optimizationTypes[config.optimizationType] }}</div>
                        </div>
                        <template #footer>
                            <small class="text-muted">Optimizes reservations through automated or semi-automated processes.</small>
                        </template>
                    </Panel>
                </div>
            </div>
            <div class="d-flex flex-wrap gap-3 gap-lg-0 row">
                <div class="col-12 col-lg-6 mb-lg-6" v-if="selectedTimeZone != 0 && auth.isSuperAdmin">
                    <Panel header="TimeZone" :pt="panelPT" class="h-100">
                        <div class="fs-2 text-capitalize">
                            <div v-if="timezones">{{ selectedTimeZone }}</div>
                            <ProgressSpinner v-else style="width: 40px; height: 40px" />
                        </div>
                    </Panel>
                </div>
                <div class="col-12 col-lg-6 mb-lg-6" v-if="showEditConfigButton">
                    <Panel :pt="panelPT" class="h-100">
                        <template #header>
                            <div class="flex justify-content-between p-panel-title text-capitalize text-muted w-100 align-items-center">
                                <span>Job status</span>
                            </div>
                        </template>
                        <div class="fs-2 text-capitalize">
                            <div v-if="config.isJobsEnable">Active</div>
                            <div v-else>Disabled</div>
                        </div>
                    </Panel>
                </div>
            </div>
        </template>
        <template v-if="auth && auth.isSuperAdmin">
            <template v-if="config && irixConfig && typeof(irixConfig) != 'string'"> 
                <hr class="mt-5">
                <div class="d-flex flex-column flex-sm-row justify-content-between mt-4">
                    <h2 class="px-2 mb-0">IRIX configurations</h2>
                    <small class="align-items-center d-flex gap-1"><i class="pi pi-exclamation-circle"></i> Note: These configurations are for reference only and cannot be altered.</small>
                </div>
                <div>
                    <div v-for="(value, type) in irixConfig" :key="type">
                        <template v-if="value && typeof(value) != 'string'"">
                            <strong class="bg-black-alpha-10 d-block mb-3 mt-3 p-2 text-uppercase">{{ type }}</strong>
                            <div class="d-flex flex-wrap gap-3 gap-lg-0 row">
                                <div class="col-12 mb-4" :class="{'col-lg-4': configDetail.code != 'allowedProvidersForOptimization'}" v-for="(configDetail, config) in value" :key="config">
                                    <Panel :header="configDetail.label" :pt="panelPT" class="h-100">
                                        <div class="fs-2 text-capitalize">
                                            <div v-if="configDetail.code == 'allowedProvidersForOptimization'" class="d-flex gap-2 flex-wrap">
                                                <Chip :label="configDetail.valueDescription[provider]" v-for="provider in configDetail.value" :key="provider" :pt="{label: {class: 'fz-r0_9'}}" />
                                            </div>
                                            <div v-else-if="configDetail.value.value && configDetail.value.currency">{{ configDetail.value.value }} {{ configDetail.value.currency }}</div>
                                            <div v-else-if="configDetail.value == true">Yes</div>
                                            <div v-else-if="configDetail.value == false">No</div>
                                            <div v-else>{{ configDetail.value }}</div>
                                        </div>
        
                                        <template #footer>
                                            <small class="text-muted">{{ configDetail.details }}</small>
                                        </template>
                                    </Panel>
                                </div>
                            </div>
                        </template>
                    </div>
                    <div class="p-error px-2 py-5" v-if="irixConfig.message">{{ irixConfig.message }}</div>
                </div>
            </template>
            <div v-else class="mt-5">
                <ProgressBar mode="indeterminate" style="height: 3px" v-if="irixConfigLoading"></ProgressBar>
            </div>
        </template>
    </main>
</template>
