const OptimizationStatusEnum = {
    "newOfferValidationFailed": "The initial validation for optimization input has failed. The selected offer is incompatible with the reservation being optimized.",
    "newBookingInitiated": "Starting the process to create a new reservation for optimization.",
    "newBookingFailedExternally": "Unable to complete the new reservation due to issues with an external service provider.",
    "newBookingFailedInternally": "Unable to complete the new reservation due to internal system issues.",
    "newBookingFinished": "The new reservation has been successfully completed. Preparing to cancel the old reservation.",
    "oldBookingPendingCancellation": "The old reservation is cancelled but awaiting confirmation from the service provider.",
    "oldBookingFailedCancellation": "Unable to cancel the old reservation.",
    "optimized": "Optimization has been successfully completed.",
    "optimizedWithWarnings": "Optimization is complete with some warnings (e.g., unexpected price increase or cancellation penalty).",
    "optimizedWithWarning": "Optimization is complete with some warnings (e.g., unexpected price increase or cancellation penalty).",
    "Repricer_CheckRoomNoLongerAvailable": "Room is no longer available.",
    "Repricer_Check_NoNewOfferORMatchedCancellationPolicy": "No new offer or matching cancellation policy found.",
    "Repricer_Check_OptimizationNotAllowed": "Optimization is not allowed for the selected client in the Repricer App.",
    "Repricer_Check_OptimizationNotAttempted": "Optimization was not attempted, but it can still be optimized.",
    "Repricer_Check_OptimizationAttemptFailedWithError": "Optimization attempt failed due to an error.",
    "Repricer_SearchSyncFailed": "Search could not be created for the specified Reservation ID.",
    "Repricer_PriceThresholdCheckFailed": "Optimization could not be created as the price threshold condition was not met.",
    "Repricer_TightCancellationPolicy": "Optimization is under a strict cancellation policy.",
    "Repricer_NotOptimizable": "Optimization is not possible due to optimization conditions or status.",
    "Repricer_OptimizationAttemptInitiated": "The optimization process has been initiated; it will be marked as successful, failed, or incomplete.",
    "Repricer_RoomNoLongerAvailable": "Room is no longer available.",
    "Repricer_NoNewOfferOrMatchedCancellationPolicy": "No new offer or matching cancellation policy found.",
    "Repricer_OptimizationNotAllowed": "Optimization is not permitted for the selected client in the Repricer App.",
    "Repricer_OptimizationNotAttempted": "Optimization was not attempted but remains possible.",
    "Repricer_OptimizationAttemptFailedWithError": "Optimization attempt failed due to an error.",
    "Repricer_ValidCancellationPolicyNotFound": "No valid cancellation policy found.",
    "Repricer_NotOptimizable": "Optimization conditions or status do not allow optimization.",
    "Repricer_OptimizationAttemptInitiated": "The optimization process is in an initial state, awaiting completion or failure.",
    "Repricer_PrebookAttemptFailed": "Failed to initiate optimization.",
    "IsOptimizableTrue": "Failed to initiate optimization.",
    "DryRunOptimizationFailed": "Failed to initiate optimization due to a dry run failure.",
    "GiataConfidenceCriteriaFailed": "Not all groups meet the confidence criteria.",
    "GroupNameNotMatched": "The group name in the reservation does not match the one in the search.",
    "GiataMappingNotFound": "Giata mapping could not be found.",
    "InvalidPrebookCriteria": "Optimization criteria are invalid.",
    "NoMatchingOffers": "No matching offers were found.",
    "RoomNotAvailable": "The room is unavailable.",
    "RoomBoardMappingNotAvailable": "No room board mapping available.",
    "PrebookAPICallFailed": "The optimization API call failed.",
    "Repricer_PriceThresholdCheckFailed_AfterDryRun": "Repricer price threshold check failed after dry run.",
    "MultiSupplierNotSETforTheClient": "Multi-supplier configuration is not set for the client."
}
    ;

export default OptimizationStatusEnum;
