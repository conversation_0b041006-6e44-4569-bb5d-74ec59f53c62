<script>
import { useUserStore } from '@/stores/useUserStore';
import { useAuthStore } from './../stores/useAuthStore'

import Button from 'primevue/button';
import Card from 'primevue/card';
import InputText from 'primevue/inputtext';
import Password from 'primevue/password';
import Toast from 'primevue/toast';

export default {
	name: "Login",
	components: { Button, Card, InputText, Password, Toast },
	data: () => {
		return {
			isLoading: false,
			loginData: { UserName: "", Password: "" },
			resetData: { email: "", token: "", newPassword: '', confirmPassword: '' },
			userEmail: '',
			formViewType: 'login', // login | forgot | reset
		}
	},
	computed: {
		resetFormDisabled() {
			let isDisabled = true;
			if (this.resetData.email && this.resetData.token && this.resetData.newPassword && this.resetData.confirmPassword && (this.resetData.newPassword === this.resetData.confirmPassword)) {
				isDisabled = false;
			}
			return isDisabled
		}
	},
	methods: {
		async onSubmit() {
			this.isLoading = true;
			const auth = useAuthStore();
			
			const response = await auth.login(this.loginData);
			this.isLoading = false;

			if (!response) {
				this.$toast.add({ severity: 'error', summary: "Something went wrong.", detail: "Please contact with support team.", life: 5000 });
				return false;
			}

			if (response.isError) {
				this.$toast.add({ severity: 'error', summary: "Something went wrong.", detail: "The provided username or password is not valid.", life: 5000 });
			}
			else {
				if (response.repricerUserId == 0) {
					this.$router.push('dashboard/admin')	
				} else {
					this.$router.push('dashboard')
				}
			}
		},
		async onForgotPassword () {
			this.isLoading = true;
			const user = useUserStore();
			const response = await user.sendEmailToken(this.resetData.email);
			if (response.isError)
				this.$toast.add({ severity: 'error', summary: response.status, detail: response.data, life: 5000 });
			else
				this.formViewType = 'reset'
			this.isLoading = false;
		},
		async onResetPassword () {
			this.isLoading = true;
			const user = useUserStore();
			const response = await user.resetPassword(this.resetData);
			if (response?.isError)
				this.$toast.add({ severity: 'error', summary: response.status, detail: response.data, life: 5000 });
			else
				this.formViewType = 'login'
			this.isLoading = false;
		}
	}
}
</script>

<template>
	<main class="d-flex w-100 p-0 min-vh-100">
		<div class="col-lg-6 d-lg-block d-none login-bg">
			<img src="./../assets/images/login1.png" alt="" class="login-bg vh-100 w-100">
		</div>
		<div class="align-items-center bg-light col-lg-6 d-flex flex-fill flex-shrink-0 px-4">
			<div class="position-absolute w-100 h-100 start-0 d-lg-none login-bg">
				<img src="./../assets/images/login1.png" class="login-bg vh-100" alt="">
			</div>
			<div class="position-relative col-12 d-flex justify-content-center">
				<Card class="align-self-center col-12" style="max-width: 30rem;">
					<template #content>
						<div class="login-form text-center">
							<img src="./../assets/images/logo-blue.png" class="logo" />
						</div>
						<form class="d-flex flex-column gap-4 login-form w-100" @submit.prevent="onSubmit" v-if="formViewType == 'login'">
							<div class="d-flex flex-column">
								<label for="username">Username</label>
								<InputText type="text" v-model="loginData.UserName" />
							</div>
							<div class="d-flex flex-column">
								<label for="username">Password</label>
								<Password inputClass="w-100" class="w-100" v-model="loginData.Password" toggleMask :feedback="false" />
							</div>
							<div class="d-flex flex-column">
								<Button type="submit" icon="pi pi-check" label="Login" :loading="isLoading" :disabled="loginData.UserName == '' || loginData.Password == ''" />
								<Button label="Forgot password?" link class="ms-auto px-0" @click="formViewType = 'forgot'" />
							</div>
						</form>
						<form class="d-flex flex-column gap-4 login-form w-100" @submit.prevent="onForgotPassword" v-if="formViewType == 'forgot'">
							<div class="d-flex flex-column">
								<label for="username">Email ID</label>
								<InputText type="email" v-model="resetData.email" />
							</div>
							<div class="d-flex flex-column">
								<Button type="submit" icon="pi pi-check" label="Get Token" :loading="isLoading" :disabled="resetData.email == ''" />
								<Button label="Back to login?" link class="ms-auto px-0" @click="formViewType = 'login'" />
							</div>
						</form>
						<form class="d-flex flex-column gap-4 login-form w-100" @submit.prevent="onResetPassword" v-if="formViewType == 'reset'">
							<div class="d-flex flex-column">
								<label for="username">Email ID</label>
								<InputText type="email" v-model="resetData.email" />
							</div>
							<div class="d-flex flex-column">
								<label for="username">Token</label>
								<InputText type="text" v-model="resetData.token" />
							</div>
							<div class="d-flex flex-column">
								<label for="username">New Password</label>
								<Password inputClass="w-100" v-model="resetData.newPassword" toggleMask />
							</div>
							<div class="d-flex flex-column">
								<label for="username">Confirm Password</label>
								<Password inputClass="w-100" v-model="resetData.confirmPassword" toggleMask :feedback="false" />
							</div>
							<div class="d-flex flex-column">
								<Button type="submit" icon="pi pi-check" label="Update" :loading="isLoading" :disabled="resetFormDisabled" />
								<Button label="Back to login?" link class="ms-auto px-0" @click="formViewType = 'login'" />
							</div>
						</form>
						<Toast />
					</template>
					<template #footer>
						<small class="d-block text-center text-muted w-100">© RePricer v1.0.0.0 powered by <a class="fw-bold p-link text-decoration-none text-secondary"
								href="https://www.dcsplus.net/" target="_blank">dcs plus</a></small>
					</template>
				</Card>
			</div>
		</div>
	</main>
</template>
