import { createRouter, createWebHistory, useRouter } from 'vue-router';
import { decodeJWT, getCookie } from '@/helpers/utils';
import constants from '@/helpers/constants';
// Views
import LoginView from '../views/LoginView.vue';
import DashboardView from '../views/DashboardView.vue';
import AdminDashboardView from '../views/admin/DashboardView.vue';
import Recommendations from '../views/Recommendations.vue';
import AllUsers from '../views/AllUsersView.vue';
import Roles from '../views/RolesView.vue';
import Configuration from '../views/ConfigurationView.vue';
import Invoice from '../views/InvoiceView.vue';
import CreateRepricer from '../views/repricer/CreateView.vue';
import UpdateRepricer from '../views/repricer/UpdateView.vue';
import ChangePassword from '../views/user/ChangePassword.vue';
import NotFound from '../views/NotFoundView.vue';
import Forbidden from '../views/ForbiddenView.vue';
import { useAuthStore } from '@/stores/useAuthStore';
import MultiSupplierView from '@/views/admin/MultiSupplierView.vue';
import MaintenanceView from '@/views/MaintenanceView.vue'; // Add Maintenance View
import Optimizations from '@/views/admin/Optimizations.vue';
import Restrictions from '@/components/repricer/Restrictions.vue';
import HitsCount from '@/views/logs/HitsCount.vue';

const router = createRouter({
	history: createWebHistory(import.meta.env.BASE_URL),
	routes: [
		{
			path: '/',
			name: 'login',
			component: LoginView
		},
		{
			path: '/dashboard/admin',
			name: 'dashboard-admin',
			component: AdminDashboardView, meta: { requiresRole: ['SuperAdmin'] }
		},
		{
			path: '/dashboard/:repricerId',
			name: 'dashboard',
			component: DashboardView, meta: { requiresRole: ['SuperAdmin'] }
		},
		{
			path: '/dashboard',
			name: 'repricerDashboard',
			component: DashboardView, meta: { requiresRole: ['User', 'Admin'] }
		},
		{
			path: '/optimizations/:type/:repricerId',
			name: 'repricerOptimizations',
			component: Recommendations, meta: { requiresRole: ['SuperAdmin'] }
		},
		{
			path: '/admin/optimizations',
			name: 'adminRepricerOptimizations',
			component: Optimizations, meta: { requiresRole: ['Developer'] }
		},
		{
			path: '/optimizations/:type',
			name: 'optimizations',
			component: Recommendations, meta: { requiresRole: ['User', 'Admin'] }
		},
		{
			path: '/allusers',
			name: 'allusers',
			component: AllUsers, meta: { requiresRole: ['Admin', 'SuperAdmin'] }
		},
		{
			path: '/roles',
			name: 'roles',
			component: Roles, meta: { requiresRole: ['SuperAdmin'] }
		},
		{
			path: '/repricer/create',
			name: 'createRepricer',
			component: CreateRepricer, meta: { requiresRole: ['SuperAdmin'] }
		},
		{
			path: '/repricer/update/:repricerId',
			name: 'updateRepricerAdmin',
			component: UpdateRepricer, meta: { requiresRole: ['SuperAdmin'] }
		},
		{
			path: '/repricer/restrictions/:repricerId',
			name: 'restrictions',
			component: Restrictions, meta: { requiresRole: ['SuperAdmin', 'Admin'] }
		},
		{
			path: '/repricer/update',
			name: 'updateRepricer',
			component: UpdateRepricer, meta: { requiresRole: ['Admin'] }
		},
		{
			path: '/invoice/:repricerId',
			name: 'invoiceAdmin',
			component: Invoice, meta: { requiresRole: ['SuperAdmin'] }
		},
		{
			path: '/invoice',
			name: 'invoice',
			component: Invoice, meta: { requiresRole: ['Admin'] }
		},
		{
			path: '/configuration/:repricerId',
			name: 'configurationAdmin',
			component: Configuration, meta: { requiresRole: ['SuperAdmin'] }
		},
		{
			path: '/configuration',
			name: 'configuration',
			component: Configuration, meta: { requiresRole: ['User', 'Admin'] }
		},
		{
			path: '/changepassword',
			name: 'changepassword',
			component: ChangePassword, meta: { requiresRole: ['User', 'Admin', 'SuperAdmin'] }
		},
		{
			path: '/report/multisupplier',
			name: 'multiSupplier',
			component: MultiSupplierView, meta: { requiresRole: ['Developer'] }
		},
		{
			path: '/logs/hits',
			name: 'hitsCount',
			component: HitsCount, meta: { requiresRole: ['Developer', 'SuperAdmin'] }
		},
		{
			path: '/forbidden',
			name: 'forbidden',
			component: Forbidden
		},
		{
			path: '/maintenance',
			name: 'maintenance',
			component: MaintenanceView
		},
		{
			path: '/:catchAll(.*)',
			name: 'notfound',
			component: NotFound
		}
	]
})

router.beforeEach((to, from, next) => {
	const isMaintenanceMode = false;
	if (isMaintenanceMode && to.name !== 'maintenance') {
		return next({ name: 'maintenance' });
	}

	if (!isMaintenanceMode && to.name == 'maintenance') {
		return next({ name: 'login' });
	}

	if (to.matched.some((record) => record.meta.requiresRole && record.meta.requiresRole.length > 0)) {
		// Check if the user is authenticated
		const token = getCookie(constants.tokenkey);


		if (!token) {
			// Redirect to login page if not authenticated
			next('/');
		} else {
			// Proceed to the intended route if authenticated
			// Check role
			const auth = useAuthStore();
			const allowedRoles = to.meta.requiresRole;
			const userRoles = auth.userInfo.roleName;
			const exists = userRoles.some(item => allowedRoles.includes(item));
			if (exists) {
				next();
			} else {
				next({ name: 'forbidden' });
			}
		}
	} else {
		// Continue for routes that do not require authentication
		next();
	}
});

export function useRouteChange(onRouteChange) {
	const router = useRouter();

	router.afterEach((to, from) => {
		onRouteChange(to, from);
	});
}

export default router
