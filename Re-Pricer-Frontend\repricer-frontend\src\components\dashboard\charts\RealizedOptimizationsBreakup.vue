<template>
    <Card class="flex-1" v-if="summary">
        <template #content>
            <div class="flex flex-column gap-4">
                <span class="text-secondary text-sm text-nowrap align-items-center d-flex">
                    Realized Optimizations Breakup
                </span>
                <Timeline :value="timeLineRealizedOptimizations" :pt="timelineConfig" layout="horizontal">
                    <template #content="slotProps">
                        <div>
                            <strong class="p-text-secondary d-block">
                                {{ slotProps.item.status }}
                            </strong>
                            <small class="d-block d-flex flex-column p-text-secondary text-nowrap">
                                <span>Gain: {{ slotProps.item.value }} <strong class="d-md-inline d-none" v-if="slotProps.item.valuePercentage">({{slotProps.item.valuePercentage.toFixed(2)}}%)</strong></span>
                                <span>Count: {{ slotProps.item.count }} <strong class="d-md-inline d-none" v-if="slotProps.item.countPercentage">({{slotProps.item.countPercentage.toFixed(2)}}%)</strong></span>
                            </small>
                        </div>
                    </template>
                </Timeline>
            </div>
        </template>
    </Card>
    <Card class="flex-1" v-else>
        <template #content>
            <div class="flex flex-column gap-4">
                <span class="text-secondary text-sm text-nowrap align-items-center d-flex">
                    Reservations
                </span>
                <Timeline :value="timeLineRealizedOptimizations" :pt="timelineConfig" layout="horizontal">
                    <template #content="slotProps">
                        <div>
                            <small class="p-text-secondary d-block">
                                <Skeleton width="8rem" height="1rem" class="mt-1"></Skeleton>
                            </small>
                            <small class="p-text-secondary d-block">
                                <Skeleton width="8rem" height="1rem" class="mt-1"></Skeleton>
                            </small>
                        </div>
                    </template>
                </Timeline>
            </div>
        </template>
    </Card>
</template>
<script>
import Card from 'primevue/card';
import Skeleton from 'primevue/skeleton';
import Timeline from 'primevue/timeline';

export default {
    name: "RealizedOptimizationsBreakup",
    components: { Card, Timeline, Skeleton },
    data() {
        return {
            timelineConfig: {
                opposite: {
                    class: "d-none"
                }
            },
        };
    },
    props: ['summary'],
    watch: {},
    computed: {
        timeLineRealizedOptimizations() {
            let data = [
                { status: 'Realized' },             // totalUniqueBookings
                { status: 'Cancelled ' },           // totalRefundableReservation
                { status: 'Final' },                // filteredReservationCount
            ];
            if (this.summary && this.summary.summarizedView) {

                const cancelled = this.summary.summarizedView.find(s => s.reportType.toLowerCase() == 'optimizedbutnotok');
                const finalCount = this.summary.realizedGainCount + (cancelled ? cancelled.realizedGainCount : 0);
                const finalGain = this.summary.realizedGain + (cancelled ? cancelled.realizedGain : 0);
                const currency = this.summary.summarizedView[0].currency;

                const CancelledValuePercentage = (cancelled.realizedGain / finalGain) * 100;
                const CancelledCountPercentage = (cancelled.realizedGainCount / finalCount) * 100;

                const RealizedValuePercentage = (this.summary.realizedGain / finalGain) * 100;
                const RealizedCountPercentage = (this.summary.realizedGainCount / finalCount) * 100;
                
                data = [
                    { status: "Total",      count: finalCount,                                  value: this.$filters.priceDisplay(finalGain, currency, true),                                   countPercentage: 0,  valuePercentage: 0 },
                    { status: "Cancelled",  count: cancelled ? cancelled.realizedGainCount : 0, value: cancelled ? this.$filters.priceDisplay(cancelled.realizedGain, currency, true) : 0,      countPercentage: CancelledCountPercentage,  valuePercentage: CancelledValuePercentage },
                    { status: "Realized",   count: this.summary.realizedGainCount,              value: this.$filters.priceDisplay(this.summary.realizedGain, currency, true),                   countPercentage: RealizedCountPercentage,  valuePercentage: RealizedValuePercentage },
                ]
            }
            return data;
        },
    },
    async mounted() {
    },
    methods: {
    }
}

</script>