<template>
	<Card class="flex-shrink-0">
		<template #title>
			<div class="d-flex justify-content-between gap-2 flex-column flex-md-row">
				<div class="text-center">Supplier optimization</div>
				<Calendar v-model="perDayOptimizationRange" view="month" dateFormat="MM yy" :manualInput="false" :maxDate="new Date()" showIcon />
			</div>
		</template>
		<template #content>
			<Chart type="bar" :data="chartData" :options="chartOptions" :pt="passThrough" />
		</template>
	</Card>
</template>
<script>
import Chart from 'primevue/chart';
import But<PERSON> from 'primevue/button';
import Card from 'primevue/card';
import Calendar from 'primevue/calendar';
import moment from 'moment';
import _ from 'lodash';
import { useRepricerStore } from '@/stores/useRepricerStore';
import { useAuthStore } from '@/stores/useAuthStore';

export default {
	name: "Supplier",
	components: { <PERSON>, <PERSON><PERSON>, <PERSON>, Calendar },
	data() {
		return {
			chartData: null,
			chartOptions: null,
			passThrough: {
				canvas: {
					class: 'w-100',
					style: 'max-height: 250px'
				}
			},
			perDayOptimizationRange: moment().startOf('month').format('MMMM yy'),
			providers: [],
			preBooks: null
		};
	},
	props: ['getDayWiseData'],
	watch: {
		perDayOptimizationRange: function (newValue) {
			this.getSupplierWiseData();
		}
	},
	mounted() {
		this.initChart();
	},
	methods: {
		async initChart() {
			this.getSupplierWiseData();
			this.chartOptions = this.setChartOptions();
		},
		async getSupplierWiseData() {
			const repricer = useRepricerStore();
			const auth = useAuthStore();
			const userInfo = auth.userInfo;

			if (new Date(this.perDayOptimizationRange).toString().toLowerCase() == "invalid date") {
				this.perDayOptimizationRange = "01 " + this.perDayOptimizationRange
			}
			const givenDate = moment(new Date(this.perDayOptimizationRange));

			const params = {
				"repricerId": userInfo.repricerUserId || this.$route.params.repricerId,
				"preBookFromDate": moment(givenDate).startOf('month').format('YYYY-MM-DD')
			}
			
			if (givenDate.isSame(moment(), 'month')) {
				params["preBookToDate"] = moment().format('YYYY-MM-DD')
			} else {
				params["preBookToDate"] = moment(givenDate).endOf('month').format('YYYY-MM-DD')
			}


			this.preBooks = await repricer.GetRepricerReport({
				...params
			});
			
			if (this.preBooks) {
				const supplierWiseProfit = [];
				this.providers = _.uniqBy(this.preBooks.data.map(p => p.reservation.supplier));
				this.providers.map(provider => {
					const currentProvider = this.preBooks.data.filter(prebook => prebook.reservation.supplier == provider);
					const profit = currentProvider.reduce((total, curVal) => {
						return total + curVal.optimizationProfit;
					}, 0);
					supplierWiseProfit.push(profit);
				})
				this.chartData = this.setChartData(supplierWiseProfit);
			}

		},
		setChartData(supplierWiseProfit) {
			const documentStyle = getComputedStyle(document.documentElement);
			return {
				labels: this.providers,

				datasets: [
					{
						data: supplierWiseProfit,
						backgroundColor: documentStyle.getPropertyValue('--primary-color'),
						borderColor: documentStyle.getPropertyValue('--primary-color'),
						barPercentage: 1,
						categoryPercentage: 0.8,
						barThickness: 10,
					}
				]
			};
		},
		setChartOptions() {
			const documentStyle = getComputedStyle(document.documentElement);
			const textColor = documentStyle.getPropertyValue('--text-color');
			const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
			const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

			return {
				indexAxis: 'x',
				maintainAspectRatio: true,
				responsive: true,
				plugins: {
					legend: {
						display: false,
						labels: {
							color: textColor
						}
					},
					datalabels: {
						display: false
					}
				},
				scales: {
					x: {
						ticks: {
							color: textColorSecondary,
							font: {
								weight: 500
							}
						},
						grid: {
							display: false,
							drawBorder: false
						},
					},
					y: {
						ticks: {
							color: textColorSecondary
						},
						grid: {
							color: surfaceBorder,
							drawBorder: false
						}
					}
				}
			};
		}
	}
}

</script>
