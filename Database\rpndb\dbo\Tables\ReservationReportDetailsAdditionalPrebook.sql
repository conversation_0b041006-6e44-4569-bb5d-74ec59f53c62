CREATE TABLE [dbo].[ReservationReportDetailsAdditionalPrebook] (
    [ReportId]                                          INT             IDENTITY (1, 1) NOT NULL,
    [Reservationid]                                     INT             NULL,
    [Repricerid]                                        INT             NULL,
    [Createdate]                                        DATETIME        NULL,
    [Reservationadultcount]                             INT             NULL,
    [Prebookadultcount]                                 INT             NULL,
    [Reservationchildages]                              VARCHAR (255)   NULL,
    [PrebookChildAges]                                  VARCHAR (255)   NULL,
    [Providers]                                         VARCHAR (255)   NULL,
    [BookingDate]                                       DATETIME        NULL,
    [ReservationPrice]                                  DECIMAL (18, 5) NULL,
    [PreBookPrice]                                      DECIMAL (18, 5) NULL,
    [ProfitAfterCancellation]                           DECIMAL (18, 5) NULL,
    [Profit]                                            DECIMAL (18, 5) NULL,
    [CurrencyFactorToEur]                               DECIMAL (18, 5) NULL,
    [ReservationRoomName]                               VARCHAR (MAX)   NULL,
    [PreBookRoomName]                                   VARCHAR (MAX)   NULL,
    [ReservationRoomBoard]                              VARCHAR (MAX)   NULL,
    [PreBookRoomBoard]                                  VARCHAR (MAX)   NULL,
    [ReservationRoomInfo]                               VARCHAR (MAX)   NULL,
    [PrebookRoomInfo]                                   VARCHAR (MAX)   NULL,
    [PreBookRoomIndex]                                  VARCHAR (256)   NULL,
    [MatchedReservationCancellationDate]                DATETIME        NULL,
    [MatchedPreBookCancellationDate]                    DATETIME        NULL,
    [MatchedReservationCancellationChargeByPolicy]      VARCHAR (MAX)   NULL,
    [MatchedPreBookCancellationChargeByPolicy]          VARCHAR (MAX)   NULL,
    [IsCancellationPolicyMatched]                       BIT             NULL,
    [CPStatus]                                          VARCHAR (50)    NULL,
    [CPDaysGain]                                        INT             NULL,
    [MatchedCancellationPolicyGain]                     DECIMAL (18, 5) NULL,
    [Token]                                             VARCHAR (MAX)   NULL,
    [AvailabilityToken]                                 VARCHAR (MAX)   NULL,
    [NumberOfRooms]                                     INT             NULL,
    [ReservationStatus]                                 VARCHAR (200)   NULL,
    [prebooksupplier]                                   VARCHAR (255)   NULL,
    [checkin]                                           DATETIME        NULL,
    [checkout]                                          DATETIME        NULL,
    [reservationhotelname]                              VARCHAR (MAX)   NULL,
    [prebookhotelname]                                  VARCHAR (MAX)   NULL,
    [prebookdestination]                                VARCHAR (MAX)   NULL,
    [reservationdestination]                            VARCHAR (MAX)   NULL,
    [roomType]                                          VARCHAR (MAX)   NULL,
    [UpdatedOn]                                         DATETIME        NULL,
    [DiffDays_Optimisation]                             INT             NULL,
    [PriceDifferenceValue]                              DECIMAL (18, 5) NULL,
    [PriceDifferencePercentage]                         DECIMAL (18, 5) NULL,
    [pricedifferencecurrency]                           VARCHAR (200)   NULL,
    [IsUsePercentage]                                   BIT             NULL,
    [traveldaysmaxsearchindays]                         INT             NULL,
    [traveldaysminsearchindays]                         INT             NULL,
    [MatchedReservationCancellationChargeByPolicyToEur] DECIMAL (18, 5) NULL,
    [MatchedPreBookCancellationChargeByPolicytoEur]     DECIMAL (18, 5) NULL,
    [ReservationGiataMappingId]                         VARCHAR (200)   NULL,
    [SearchGiataMappingId]                              VARCHAR (200)   NULL,
    [PreBookGiataPropertyName]                          VARCHAR (1000)  NULL,
    [ReservationGiataPropertyName]                      VARCHAR (1000)  NULL,
    [ReservationRoomBoardGroup]                         VARCHAR (1000)  NULL,
    [PrebookRoomBoardGroup]                             VARCHAR (1000)  NULL,
    [ReservationCancellationType]                       VARCHAR (100)   NULL,
    [PreBookCancellationType]                           VARCHAR (100)   NULL,
    [CancellationPolicyRemark]                          VARCHAR (100)   NULL,
    [ResellerName]                                      VARCHAR (256)   NULL,
    [ResellerCode]                                      VARCHAR (128)   NULL,
    [ResellerType]                                      VARCHAR (128)   NULL,
    [PreBookId]                                         BIGINT          NULL,
    [PrebookRank]                                       INT             NULL,  -- NEW: Ranking 2, 3, 4...
    [PrimaryPrebookId]                                  BIGINT          NULL,  -- NEW: Reference to primary prebook
    [cancellationpolicygainconvertedtoeur]              DECIMAL (18, 5) NULL,  -- COMPATIBILITY: Missing from original
    [isActive]                                          BIT             DEFAULT ((1)) NULL,  -- COMPATIBILITY: Missing from original
    [prebookTableId]                                    BIGINT          NULL,  -- COMPATIBILITY: Missing from original
    [IsOptimized]                                       BIT             DEFAULT ((0)) NULL,  -- COMPATIBILITY: Missing from original
    CONSTRAINT [PK_ReservationReportDetailsAdditionalPrebook] PRIMARY KEY CLUSTERED ([ReportId] ASC)
);

GO
CREATE NONCLUSTERED INDEX [IDX_RepricerID_CreateDate_ReservationReportDetailsAdditionalPrebook]
    ON [dbo].[ReservationReportDetailsAdditionalPrebook]([Repricerid] ASC, [Createdate] ASC);

GO
CREATE NONCLUSTERED INDEX [IDX_BookingDate_ReservationReportDetailsAdditionalPrebook]
    ON [dbo].[ReservationReportDetailsAdditionalPrebook]([Repricerid] ASC, [BookingDate] ASC);

GO
CREATE NONCLUSTERED INDEX [IDX_ReservationReportDetailsAdditionalPrebook]
    ON [dbo].[ReservationReportDetailsAdditionalPrebook]([Repricerid] ASC, [ReservationStatus] ASC);

GO
CREATE NONCLUSTERED INDEX [IDX_CreateDate_ReservationReportDetailsAdditionalPrebook]
    ON [dbo].[ReservationReportDetailsAdditionalPrebook]([Createdate] ASC);

GO
CREATE NONCLUSTERED INDEX [IDX_ReservationId_RepricerId_AdditionalPrebook]
    ON [dbo].[ReservationReportDetailsAdditionalPrebook]([Reservationid] ASC, [Repricerid] ASC);

GO
CREATE NONCLUSTERED INDEX [IDX_PrimaryPrebookId_AdditionalPrebook]
    ON [dbo].[ReservationReportDetailsAdditionalPrebook]([PrimaryPrebookId] ASC);
