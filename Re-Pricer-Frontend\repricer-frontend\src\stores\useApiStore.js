import constants from '@/helpers/constants';
import { setCookieWithTime, getCookie } from '@/helpers/utils';
import axios from 'axios';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

// const baseURL = `https://rpnapidcs.azurewebsites.net`;
const baseURL = `https://localhost:7145`;

// Function to handle errors and log them
const handleError = (error) => {
	// console.error('API Error:', error);
	// throw error;
	let output = {
		isError: true
	}
	if (error.response) {
		output = {
			...output,
			...error.response
		}
	}
	if (typeof (error.data) == "string") {
		output = {
			...output,
			data: error.data
		}
	} else if (error.data) {
		output = {
			...output,
			...error.data
		}
	}
	return output;
};

// Function to handle successful responses
const handleSuccess = (response) => response.data;

// Function to create an Axios instance with common configurations
const createAxiosInstance = (withoutHeader, customHeader) => {
	let headers = {
		'Content-Type': 'multipart/form-data',
		// You can add additional headers here if needed
	}

	const token = getCookie(constants.tokenkey);
	if (token) {
		const tokenData = JSON.parse(atob(token.split('.')[1]));
		const expirationTime = tokenData.exp * 1000; // Convert expiration time to milliseconds
		const currentTime = Date.now();

		if (currentTime >= expirationTime) {
			// Token has expired
			const router = useRouter();
			router.push('/');
		} else {
			if (!withoutHeader) {
				headers["Authorization"] = `Bearer ${token}`;
				headers['Content-Type'] = 'application/json';
			}
		}

	}
	headers = { ...headers, ...customHeader };
	const instance = axios.create({
		baseURL,
		headers: headers
	});

	// Add request interceptors for common configurations

	// Add response interceptors for common error handling
	instance.interceptors.response.use(handleSuccess, handleError);

	return instance;
};

// Function to make a GET request
const get = async (url) => {
	try {
		const response = await createAxiosInstance().get(url);
		return response;
	} catch (error) {
		return handleError(error);
	}
};

// Function to make a POST request
const post = async (url, data, withoutHeader, customHeader) => {
	try {
		const response = await createAxiosInstance(withoutHeader, customHeader).post(url, data);
		if (response.isError) {
			return handleError(response);
		} else {
			if (["/token", "/refresh-token"].indexOf(url) >= 0 && response.access_token) {
				setCookieWithTime(constants.tokenkey, response.access_token.split(' ')[1], response.expires)
			}
			return response;
		}
	} catch (error) {
		return handleError(error);
	}
};

// Function to make a PUT request
const put = async (url, data) => {
	try {
		const response = await createAxiosInstance().put(url, data);
		return response;
	} catch (error) {
		return handleError(error);
	}
};

// Function to make a DELETE request
const remove = async (url) => {
	try {
		const response = await createAxiosInstance().delete(url);
		return response;
	} catch (error) {
		return handleError(error);
	}
};

// Export the functions for use in components
export const useApiStore = () => {
	return {
		get,
		post,
		put,
		remove,
	};
};
