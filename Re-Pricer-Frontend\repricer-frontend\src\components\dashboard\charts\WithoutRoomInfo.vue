<template>
	<Card class="flex-fill" :pt="pt">
		<template #subtitle>
			<div class="align-items-center d-flex justify-content-between">
				<strong class="fz-r0_9">Without Room Info</strong>
			</div>
			<div class="d-flex justify-content-between w-100">
				<small>comparing reservation detail with optimizable offer, except Room Info. To ensures we may optimize more reservations without taking unnecessary risks.</small>
			</div>
		</template>
		<template #content>
			<Divider class="my-2"></Divider>
			<div class="d-flex justify-content-between">
				<div class="align-self-center gap-2" v-if="summary">
					<div class="fs-4 text-nowrap">{{ $filters.priceDisplay(currentSummary.profit, currentSummary.currency, true) }} </div>
					<div class="fz-r0_8 text-muted">{{ currentSummary.reservationsCount }}</div>
				</div>
				<div class="align-self-center gap-2" v-else>
					<Skeleton width="8rem" height="2rem" class="mt-1"></Skeleton>
					<Skeleton width="8rem" height="1rem" class="mt-1"></Skeleton>
				</div>
			</div>
			<Divider class="my-2"></Divider>
			<div class="d-flex">
				<Button icon="pi pi-external-link" @click="redirectTo('/optimizations/without-room-info')" class="ms-auto"
					:label="'Show more'" size="small" aria-label="Submit" link />
			</div>
		</template>
	</Card>
</template>
<script>
import Button from 'primevue/button';
import Card from 'primevue/card';
import Divider from 'primevue/divider';
import Skeleton from 'primevue/skeleton';

export default {
	name: "WithoutRoomInfo",
	components: { Button, Card, Divider, Skeleton },
	data() {
		return {
		};
	},
	computed: {
		currentSummary() {
			let data = {
				"profit": 0,
				"currency": "EUR",
				"reservationsCount": 0,
			};
			if (this.summary && this.summary.summarizedView) {
				let filtered = this.summary.summarizedView.find(s => s.reportType == "WithoutRoomInfoMatching")
				if (filtered) {
					return filtered
				} else {
					return data
				}
			} else {
				return data;
			}

		}
	},
	props: ['pt', 'DataTablePT', 'params', 'redirectTo', 'summary'],
	watch: {

	},
	async mounted() {
	},
	methods: {
	}
}

</script>
