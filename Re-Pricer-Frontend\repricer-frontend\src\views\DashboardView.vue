<script>
// Packages
import _ from 'lodash';
import moment from 'moment';
// Stores
import { useAuthStore } from '@/stores/useAuthStore';
import { useRepricerStore } from '@/stores/useRepricerStore';
// components
import RealizedOptimizationsBreakup from '@/components/dashboard/charts/RealizedOptimizationsBreakup.vue';
import TotalReservations from '@/components/dashboard/charts/TotalReservations.vue';
import JobTime from '@/components/dashboard/charts/JobTime.vue';
import EdgeCasePrice from '@/components/dashboard/charts/EdgeCasePrice.vue';
import WithoutRoomInfo from '@/components/dashboard/charts/WithoutRoomInfo.vue';
import BetterCancellationPolicy from '@/components/dashboard/charts/BetterCancellationPolicy.vue';
import GainMeters from '@/components/dashboard/charts/GainMeters.vue';
import SummaryTiles from '@/components/dashboard/charts/SummaryTiles.vue';
import EdgeCaseCancellationPolicy from '@/components/dashboard/charts/EdgeCaseCancellationPolicy.vue';
import Supplier from '@/components/dashboard/charts/Supplier.vue';
import RealizedGain from '@/components/dashboard/charts/RealizedGain.vue';
import CancellationPolicy from '@/components/dashboard/CancellationPolicy.vue';
import Loader from '@/components/Loader.vue';
import ActiveGainSummary from '@/components/dashboard/charts/ActiveGainSummary.vue';
import Card from 'primevue/card';
import Divider from 'primevue/divider';
import Knob from 'primevue/knob';
import Calendar from 'primevue/calendar';
import Message from 'primevue/message';
import Tag from 'primevue/tag';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';


export default {
	name: "Dashboard",
	components: { JobTime, Loader, EdgeCasePrice, EdgeCaseCancellationPolicy, ActiveGainSummary, GainMeters, SummaryTiles, Supplier, Card, Divider, Knob, Calendar, Message, RealizedGain, DataTable, Column, CancellationPolicy, Tag, Button,
		RealizedOptimizationsBreakup, TotalReservations, WithoutRoomInfo, BetterCancellationPolicy
	 },
	data() {
		return {
			isDeveloper: false,
			mainLoading: false,
			userInfo: null,
			selectedDatesForPrebook: [],
			perDayOptimizationRange: moment().startOf('month').format('MMMM yy'),
			summary: null,
			repricerInfo: null,
			passThroughEdgeCase: {
				content: {
					class: 'd-flex justify-content-between flex-column w-100'
				},
				caption: {
					class: 'gap-0'
				},
				title: {
					class: 'd-flex justify-content-between'
				},
				body: {
					class: "justify-content-between flex-fill p-0"
				},
				root: {
					class: 'shadow-none col col-12 col-md-4'
				}
			},
			passThroughActiveGain: {
				body: {
					class: "justify-content-between h-100"
				},
				root: {
					class: "col-lg-6"
				}
			},
			getEdgeCasesDT: {
				footer: {
					class: 'd-flex justify-content-end border-bottom-0 pb-0 px-0'
				},
				thead: {
					class: "fz-r0_8"
				}
			},
			gainMorePassThrough: {
				body: {
					class: "h-100"
				},
				content: {
					class: "row"
				}
			}
		}
	},
	computed: {
		perDayOptimizationAllDates() {
			return this.getAllDatesOfMonth(this.perDayOptimizationRange)
		},
		getPrebookParams() {
			let params = {};
			if (this.userInfo) {
				params = {
					"repricerId": this.userInfo.repricerUserId || this.$route.params.repricerId
				}

				if (this.selectedDatesForPrebook[0]) {
					params["fromDate"] = moment(this.selectedDatesForPrebook[0]).format("YYYY-MM-DD");
				}

				if (this.selectedDatesForPrebook[1]) {
					params["toDate"] = moment(this.selectedDatesForPrebook[1]).format("YYYY-MM-DD");
				}
			}

			return params
		},
		lastSevenDays() {
			const lastSevenDays = [];
			for (let i = 6; i >= 0; i--) {
				const date = moment().subtract(i, 'days').format('YYYY-MM-DD');
				lastSevenDays.push(date);
			}
			return lastSevenDays;
		}
	},
	methods: {
		getAllDatesOfMonth(firstDateOfMonth) {
			// Get the last date of the month
			const lastDateOfMonth = moment(new Date(firstDateOfMonth)).clone().endOf('month');

			// Initialize an array to store all dates of the month
			const allDatesOfMonth = [];

			// Loop from the first date to the last date of the month
			let currentDate = moment(new Date(firstDateOfMonth)).clone();
			while (currentDate.isSameOrBefore(lastDateOfMonth)) {
				allDatesOfMonth.push(currentDate.format('YYYY-MM-DD'));
				currentDate.add(1, 'day'); // Move to the next day
			}

			return allDatesOfMonth;
		},
		prebooksFromSelectedMonth(notifiedPreBooks, perDayOptimizationRange, uptoCurrentDate) {
			if (notifiedPreBooks) {
				const dateFromLast7Days = moment(new Date(perDayOptimizationRange));

				let filtered = notifiedPreBooks.filter(notification => {
					let createdDate = new Date(notification.createdDate);
					if (uptoCurrentDate) {
						return moment(createdDate).isSameOrAfter(dateFromLast7Days, 'month')
					} else {
						return moment(createdDate).isSame(dateFromLast7Days, 'month')
					}
				})
				filtered.sort((a, b) => {
					// Convert 'createdDate' strings to Date objects for comparison
					const dateA = new Date(a.createdDate);
					const dateB = new Date(b.createdDate);

					// Compare the dates and return the result
					return dateA - dateB;
				});
				return filtered;
			} else {
				return []
			}
		},
		redirectTo(type) {
			let path = type;
			if (this.$route.params.repricerId) {
				path = `${type}/${this.$route.params.repricerId}`;
			}
			this.$router.push({ path: path })
		},
		async getSummaryByDate(isFresh) {
			const auth = useAuthStore();
			this.isDeveloper = auth.userInfo.roleName.indexOf("Developer") >= 0;
			const userInfo = auth.userInfo;
			this.userInfo = auth.userInfo;
			const repricer = useRepricerStore();
			const params = {
				RepricerId: userInfo.repricerUserId || this.$route.params.repricerId,
			}

			if (isFresh) {
				params["isCached"] = false
			}

			this.summary = await repricer.GetRepricerReportSummary(params);
            const repricerID = this.$route.params.repricerId || repricer.repricer.repricerUserID;
            this.repricerInfo = await repricer.getRePricerById(repricerID);
			this.mainLoading = false;
		}
	},
	mounted() {
		// this.getSummaryByDate();
	},
	watch: {
		'$route.params.repricerId': {
			immediate: true,
			handler(newId, oldId) {
				this.mainLoading = true;
				this.summary = null;
				this.getSummaryByDate();
			}
		}
	}
}

</script>

<template>
	<main class="container d-flex my-3">
		<Loader v-if="mainLoading" />
		<div v-else class="w-100 d-flex flex-column justify-content-between gap-4 mb-4">
			<div class="w-100 d-flex flex-column justify-content-between gap-4">
				<div class="flex justify-content-end">
					<Button v-if="isDeveloper" icon="pi pi-refresh" label="Fetch latest data" @click="getSummaryByDate(true)"></Button>
				</div>
				<div class="d-flex gap-3">
					<SummaryTiles :summary="summary" :getSummaryByDate="getSummaryByDate" :redirectTo="redirectTo" />
				</div>
				<div class="d-flex flex-column gap-4 justify-content-between flex-lg-row">
					<!-- <ActiveGainSummary :pt="passThroughActiveGain" :params="getPrebookParams" :redirectTo="redirectTo"
						:summary="summary" :getSummaryByDate="getSummaryByDate" />  -->
					<Card :pt="gainMorePassThrough" class="w-100">
						<template #title>
							<div class="text-center">you can gain more</div>
						</template>
						<template #content>
							<EdgeCaseCancellationPolicy :pt="passThroughEdgeCase" :DataTablePT="getEdgeCasesDT" :summary="summary" :params="getPrebookParams" :redirectTo="redirectTo" />
							<EdgeCasePrice :pt="passThroughEdgeCase" :DataTablePT="getEdgeCasesDT" :repricerInfo="repricerInfo" :summary="summary" :params="getPrebookParams" :redirectTo="redirectTo" />
							<!-- <WithoutRoomInfo :pt="passThroughEdgeCase" :DataTablePT="getEdgeCasesDT" :summary="summary" :params="getPrebookParams" :redirectTo="redirectTo" /> -->
							<BetterCancellationPolicy :pt="passThroughEdgeCase" :DataTablePT="getEdgeCasesDT" :summary="summary" :params="getPrebookParams" :redirectTo="redirectTo" />
						</template>
					</Card>
				</div>
				<RealizedOptimizationsBreakup :summary="summary" />
				<TotalReservations :summary="summary" />
				<!-- <MonthWisePreBooks :redirectTo="redirectTo" :getAllDatesOfMonth="getAllDatesOfMonth" :getDayWiseData="prebooksFromSelectedMonth" /> -->
				<Supplier :redirectTo="redirectTo" :getDayWiseData="prebooksFromSelectedMonth" />
			</div>
		</div>
	</main>
</template>

<style>
.w-min-25 {
    min-width: 25%;
}
.w-max-25 {
    max-width: 25%;
}
</style>