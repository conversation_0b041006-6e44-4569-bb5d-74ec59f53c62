import { defineStore } from 'pinia'
import { useApiStore } from './useApiStore';

export const useUserStore = defineStore({
	// Set persist to true
	persist: true,
	id: "user",
	state: () => ({
		users: [],
		roles: [],
	}),
	actions: {
		async getAllUsers(latest) {
			if (this.users.length > 0 && !latest) {
				return this.users
			}
			const api = useApiStore();
			const response = await api.get("/api/Admin/ViewAllUsers");
			if (response) {
				this.users = response
			}
			return response;
		},
		async createUser(userInfo) {
			const api = useApiStore();
			const response = await api.post("/api/Admin/CreateUser", userInfo);
			return response;
		},
		async updateUser(userInfo) {
			const api = useApiStore();
			const response = await api.put("/api/Admin/UpdateUser", userInfo);
			return response;
		},
		async deleteUser(userId) {
			const api = useApiStore();
			const response = await api.remove(`/api/Admin/DeleteUser/${userId}`);
			return response;
		},
		async unlockUser(UserName) {
			const api = useApiStore();
			const response = await api.post(`/api/Admin/UnlockUser/${UserName}`);
			return response;
		},
		async getPrebook(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/User/${payload.type}`, payload.params);
			return response;
		},
		async updatePrebook(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/User/UpdateReservation`, payload);
			return response;
		},
		async downloadReport(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/User/GetReport`, payload);
			return response;
		},
		async changePassword(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/User/ChangePassword`, payload);
			return response;
		},
		async sendEmailToken(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/User/SendEmailToken?email=${payload}`, null, true);
			return response;
		},
		async resetPassword(payload) {
			const api = useApiStore();
			const response = await api.post(`/api/User/ResetPassword`, payload, true, {
				"Content-Type": "application/json"
			});
			return response;
		}
	}
});