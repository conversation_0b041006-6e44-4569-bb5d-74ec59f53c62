-- =============================================
-- Test Script for Multiple Prebook Functionality
-- Author: Augment Agent
-- Date: 2025-01-27
-- Description: Test the multiple prebook implementation
-- =============================================

-- Test 1: Verify the stored procedure exists and can be executed
PRINT 'Test 1: Testing usp_get_AdditionalPrebookOptions stored procedure'
BEGIN TRY
    EXEC [dbo].[usp_get_AdditionalPrebookOptions] @RepricerId = 99
    PRINT 'SUCCESS: Stored procedure executed without errors'
END TRY
BEGIN CATCH
    PRINT 'ERROR: ' + ERROR_MESSAGE()
END CATCH

-- Test 2: Check if ReservationReportDetailsAdditionalPrebook table exists
PRINT 'Test 2: Checking if ReservationReportDetailsAdditionalPrebook table exists'
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ReservationReportDetailsAdditionalPrebook')
    PRINT 'SUCCESS: ReservationReportDetailsAdditionalPrebook table exists'
ELSE
    PRINT 'WARNING: ReservationReportDetailsAdditionalPrebook table does not exist yet'

-- Test 3: Check if usp_upd_reservationreport_AdditionalPrebook procedure exists
PRINT 'Test 3: Checking if usp_upd_reservationreport_AdditionalPrebook procedure exists'
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = 'usp_upd_reservationreport_AdditionalPrebook')
    PRINT 'SUCCESS: usp_upd_reservationreport_AdditionalPrebook procedure exists'
ELSE
    PRINT 'WARNING: usp_upd_reservationreport_AdditionalPrebook procedure does not exist yet'

-- Test 4: Test with specific reservation ID if data exists
PRINT 'Test 4: Testing with specific parameters'
BEGIN TRY
    EXEC [dbo].[usp_get_AdditionalPrebookOptions] 
        @RepricerId = 99,
        @ReservationId = '12345',
        @FromDate = '2025-01-01',
        @ToDate = '2025-01-27'
    PRINT 'SUCCESS: Stored procedure with parameters executed without errors'
END TRY
BEGIN CATCH
    PRINT 'ERROR: ' + ERROR_MESSAGE()
END CATCH

-- Test 5: Check the structure of the main ReservationReportDetails table
PRINT 'Test 5: Checking ReservationReportDetails table structure'
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ReservationReportDetails')
BEGIN
    PRINT 'SUCCESS: ReservationReportDetails table exists'
    
    -- Check if we have any data for testing
    DECLARE @RecordCount INT
    SELECT @RecordCount = COUNT(*) FROM ReservationReportDetails WHERE Repricerid = 99
    PRINT 'INFO: Found ' + CAST(@RecordCount AS VARCHAR(10)) + ' records for RepricerId 99'
    
    IF @RecordCount > 0
    BEGIN
        -- Show sample data structure
        PRINT 'Sample data from ReservationReportDetails:'
        SELECT TOP 1 
            Repricerid, 
            Reservationid, 
            prebooksupplier, 
            PreBookPrice,
            Createdate
        FROM ReservationReportDetails 
        WHERE Repricerid = 99
        ORDER BY Createdate DESC
    END
END
ELSE
    PRINT 'ERROR: ReservationReportDetails table does not exist'

PRINT 'Test completed. Check the output above for any errors or warnings.'
