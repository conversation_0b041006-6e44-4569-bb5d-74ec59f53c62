-- =============================================
-- TEST DUPLICATE PREVENTION FOR ADDITIONAL PREBOOK FUNCTIONALITY
-- Author: Augment Agent
-- Date: 2025-01-27
-- Description: Verify that duplicate rows are prevented
-- =============================================

USE [rpndb]
GO

PRINT '=========================================='
PRINT 'TESTING DUPLICATE PREVENTION'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

DECLARE @TestRepricerId INT = 1;

-- Clear existing test data
DELETE FROM ReservationReportDetailsAdditionalPrebook WHERE Repricerid = @TestRepricerId;
PRINT 'Cleared existing test data for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10))

-- Check source data
DECLARE @SourceCount INT;
SELECT @SourceCount = COUNT(*)
FROM ReservationTable 
WHERE RepricerID = @TestRepricerId;

PRINT 'Source data in ReservationTable: ' + CAST(@SourceCount AS VARCHAR(10)) + ' records'

IF @SourceCount > 0
BEGIN
    PRINT ''
    PRINT 'TEST 1: First execution of stored procedure...'
    
    BEGIN TRY
        -- Execute the procedure first time
        EXEC dbo.usp_upd_reservationreport_AdditionalPrebook 
            @Repricerid = @TestRepricerId, 
            @Reservationid = NULL;
        
        DECLARE @FirstRunCount INT;
        SELECT @FirstRunCount = COUNT(*)
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId;
        
        PRINT '✅ First run completed successfully!'
        PRINT 'Records created in first run: ' + CAST(@FirstRunCount AS VARCHAR(10))
        
        -- Check for duplicates within first run
        DECLARE @FirstRunDuplicates INT;
        SELECT @FirstRunDuplicates = COUNT(*) - COUNT(DISTINCT CONCAT(Reservationid, '_', PrebookRank, '_', PreBookId))
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId;
        
        IF @FirstRunDuplicates > 0
        BEGIN
            PRINT '❌ ERROR: Found ' + CAST(@FirstRunDuplicates AS VARCHAR(10)) + ' duplicates in first run!'
        END
        ELSE
        BEGIN
            PRINT '✅ PASS: No duplicates found in first run'
        END
        
        PRINT ''
        PRINT 'TEST 2: Second execution (should replace, not duplicate)...'
        
        -- Execute the procedure second time
        EXEC dbo.usp_upd_reservationreport_AdditionalPrebook 
            @Repricerid = @TestRepricerId, 
            @Reservationid = NULL;
        
        DECLARE @SecondRunCount INT;
        SELECT @SecondRunCount = COUNT(*)
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId;
        
        PRINT '✅ Second run completed successfully!'
        PRINT 'Records after second run: ' + CAST(@SecondRunCount AS VARCHAR(10))
        
        -- Check if counts are the same (no duplicates added)
        IF @SecondRunCount = @FirstRunCount
        BEGIN
            PRINT '✅ PASS: Record count unchanged (duplicates prevented)'
        END
        ELSE
        BEGIN
            PRINT '❌ ERROR: Record count changed from ' + CAST(@FirstRunCount AS VARCHAR(10)) + ' to ' + CAST(@SecondRunCount AS VARCHAR(10))
        END
        
        -- Check for duplicates after second run
        DECLARE @SecondRunDuplicates INT;
        SELECT @SecondRunDuplicates = COUNT(*) - COUNT(DISTINCT CONCAT(Reservationid, '_', PrebookRank, '_', PreBookId))
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId;
        
        IF @SecondRunDuplicates > 0
        BEGIN
            PRINT '❌ ERROR: Found ' + CAST(@SecondRunDuplicates AS VARCHAR(10)) + ' duplicates after second run!'
        END
        ELSE
        BEGIN
            PRINT '✅ PASS: No duplicates found after second run'
        END
        
        PRINT ''
        PRINT 'TEST 3: Detailed duplicate analysis...'
        
        -- Check for specific types of duplicates
        SELECT 
            'Duplicate by Reservation+Rank' as DuplicateType,
            COUNT(*) as DuplicateCount
        FROM (
            SELECT Reservationid, PrebookRank, COUNT(*) as cnt
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY Reservationid, PrebookRank
            HAVING COUNT(*) > 1
        ) dups
        
        UNION ALL
        
        SELECT 
            'Duplicate by PreBookId' as DuplicateType,
            COUNT(*) as DuplicateCount
        FROM (
            SELECT PreBookId, COUNT(*) as cnt
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
              AND PreBookId IS NOT NULL
            GROUP BY PreBookId
            HAVING COUNT(*) > 1
        ) dups
        
        UNION ALL
        
        SELECT 
            'Duplicate by Reservation+Supplier+Rank' as DuplicateType,
            COUNT(*) as DuplicateCount
        FROM (
            SELECT Reservationid, prebooksupplier, PrebookRank, COUNT(*) as cnt
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY Reservationid, prebooksupplier, PrebookRank
            HAVING COUNT(*) > 1
        ) dups;
        
        PRINT ''
        PRINT 'TEST 4: Data integrity checks...'
        
        -- Check rank sequence integrity
        SELECT 
            Reservationid,
            COUNT(*) as PrebookCount,
            MIN(PrebookRank) as MinRank,
            MAX(PrebookRank) as MaxRank,
            STRING_AGG(CAST(PrebookRank AS VARCHAR), ',') as RankSequence
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId
        GROUP BY Reservationid
        ORDER BY Reservationid;
        
        -- Check supplier diversity
        PRINT ''
        PRINT 'TEST 5: Supplier diversity per reservation...'
        SELECT 
            Reservationid,
            COUNT(DISTINCT prebooksupplier) as UniqueSuppliers,
            COUNT(*) as TotalPrebooks,
            STRING_AGG(prebooksupplier, ', ') as Suppliers
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId
        GROUP BY Reservationid
        HAVING COUNT(*) > 1
        ORDER BY Reservationid;
        
        PRINT ''
        PRINT 'TEST 6: Sample data verification...'
        SELECT TOP 10
            ReportId,
            Reservationid,
            PrebookRank,
            prebooksupplier,
            CAST(Profit AS DECIMAL(10,2)) as Profit,
            PreBookId,
            PrimaryPrebookId
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId
        ORDER BY Reservationid, PrebookRank;
        
    END TRY
    BEGIN CATCH
        PRINT '❌ ERROR: ' + ERROR_MESSAGE()
        PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR(10))
        PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR(10))
    END CATCH
    
END
ELSE
BEGIN
    PRINT '⚠️ No source data found for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10))
    PRINT 'Please use a different RepricerId that has data'
END

PRINT ''
PRINT '=========================================='
PRINT 'DUPLICATE PREVENTION TEST COMPLETED'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='
