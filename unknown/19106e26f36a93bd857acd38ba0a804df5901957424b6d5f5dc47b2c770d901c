-- =============================================
-- TEST COLUMN FIX FOR ADDITIONAL PREBOOK FUNCTIONALITY
-- Author: Augment Agent
-- Date: 2025-01-27
-- Description: Test that PrebookRank column error is fixed
-- =============================================

USE [rpndb]
GO

PRINT '=========================================='
PRINT 'TESTING COLUMN FIX - PrebookRank'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

DECLARE @TestRepricerId INT = 1;

-- Clear existing test data
DELETE FROM ReservationReportDetailsAdditionalPrebook WHERE Repricerid = @TestRepricerId;
PRINT 'Cleared existing test data for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10))

-- Check source data
DECLARE @SourceCount INT;
SELECT @SourceCount = COUNT(*)
FROM ReservationTable 
WHERE RepricerID = @TestRepricerId;

PRINT 'Source data in ReservationTable: ' + CAST(@SourceCount AS VARCHAR(10)) + ' records'

IF @SourceCount > 0
BEGIN
    PRINT ''
    PRINT 'Testing stored procedure execution...'
    
    BEGIN TRY
        -- Execute the procedure
        EXEC dbo.usp_upd_reservationreport_AdditionalPrebook 
            @Repricerid = @TestRepricerId, 
            @Reservationid = NULL;
        
        DECLARE @ResultCount INT;
        SELECT @ResultCount = COUNT(*)
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId;
        
        PRINT '✅ SUCCESS: Procedure executed without column errors!'
        PRINT 'Additional prebooks created: ' + CAST(@ResultCount AS VARCHAR(10))
        
        IF @ResultCount > 0
        BEGIN
            PRINT ''
            PRINT 'VERIFICATION: Sample results with PrebookRank...'
            
            SELECT TOP 5
                ReportId,
                Reservationid,
                PrebookRank,
                prebooksupplier,
                CAST(Profit AS DECIMAL(10,2)) as Profit,
                CAST(Createdate AS DATE) as CreateDate
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            ORDER BY Reservationid, PrebookRank;
            
            -- Check rank distribution
            PRINT ''
            PRINT 'RANK DISTRIBUTION:'
            SELECT 
                PrebookRank,
                COUNT(*) as RecordCount
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY PrebookRank
            ORDER BY PrebookRank;
            
            -- Check for one row per supplier per reservation
            PRINT ''
            PRINT 'SUPPLIER UNIQUENESS CHECK:'
            SELECT 
                Reservationid,
                prebooksupplier,
                COUNT(*) as RowCount,
                CASE WHEN COUNT(*) > 1 THEN 'DUPLICATE' ELSE 'UNIQUE' END as Status
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY Reservationid, prebooksupplier
            ORDER BY Reservationid, prebooksupplier;
            
        END
        ELSE
        BEGIN
            PRINT '⚠️ No additional prebooks created, but procedure executed successfully (no column errors)'
        END
        
    END TRY
    BEGIN CATCH
        PRINT '❌ ERROR: ' + ERROR_MESSAGE()
        PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR(10))
        PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR(10))
        
        -- Check if it's still the PrebookRank column error
        IF ERROR_MESSAGE() LIKE '%PrebookRank%'
        BEGIN
            PRINT '🔥 STILL COLUMN ERROR: PrebookRank column issue not fixed!'
        END
        ELSE
        BEGIN
            PRINT '✅ COLUMN FIX SUCCESS: Different error (not PrebookRank column issue)'
        END
    END CATCH
    
END
ELSE
BEGIN
    PRINT '⚠️ No source data found for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10))
    PRINT 'Testing with a different RepricerId...'
    
    -- Try to find a repricer with data
    SELECT TOP 5 
        RepricerID,
        COUNT(*) as RecordCount
    FROM ReservationTable
    WHERE CAST(createdate AS DATE) >= DATEADD(day, -7, GETDATE())  -- Recent data
    GROUP BY RepricerID
    ORDER BY COUNT(*) DESC;
    
    PRINT 'Use one of the above RepricerIDs for testing'
END

PRINT ''
PRINT '=========================================='
PRINT 'COLUMN FIX TEST COMPLETED'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='
