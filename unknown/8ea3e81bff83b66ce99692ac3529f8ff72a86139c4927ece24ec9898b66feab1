-- =============================================
-- TEST SCRIPT FOR ADDITIONAL PREBOOK FUNCTIONALITY
-- Author: Augment Agent
-- Date: 2025-01-27
-- Description: Test the fixed stored procedure with RepricerId = 1
-- =============================================

USE [rpndb]
GO

PRINT '=========================================='
PRINT 'TESTING ADDITIONAL PREBOOK FUNCTIONALITY'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

-- Test parameters
DECLARE @TestRepricerId INT = 1;
DECLARE @TestReservationId INT = NULL; -- Test all reservations

PRINT 'Testing with RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10))

-- Clear any existing test data
DELETE FROM ReservationReportDetailsAdditionalPrebook WHERE Repricerid = @TestRepricerId;
PRINT 'Cleared existing test data'

-- Check source data availability
DECLARE @SourceDataCount INT;
SELECT @SourceDataCount = COUNT(*)
FROM ReservationTable 
WHERE RepricerID = @TestRepricerId;

PRINT 'Source data in ReservationTable: ' + CAST(@SourceDataCount AS VARCHAR(10)) + ' records'

IF @SourceDataCount = 0
BEGIN
    SELECT @SourceDataCount = COUNT(*)
    FROM ReservationTablelog 
    WHERE RepricerID = @TestRepricerId;
    
    PRINT 'Source data in ReservationTablelog: ' + CAST(@SourceDataCount AS VARCHAR(10)) + ' records'
END

IF @SourceDataCount > 0
BEGIN
    PRINT ''
    PRINT 'Executing stored procedure...'
    
    -- Execute the procedure
    BEGIN TRY
        EXEC dbo.usp_upd_reservationreport_AdditionalPrebook 
            @Repricerid = @TestRepricerId, 
            @Reservationid = @TestReservationId;
        
        PRINT '✅ Procedure executed successfully!'
        
        -- Check results
        DECLARE @ResultCount INT;
        SELECT @ResultCount = COUNT(*)
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId;
        
        PRINT 'Additional prebooks created: ' + CAST(@ResultCount AS VARCHAR(10))
        
        IF @ResultCount > 0
        BEGIN
            PRINT ''
            PRINT '✅ SUCCESS: Data was populated successfully!'
            
            -- Show summary
            PRINT ''
            PRINT 'SUMMARY BY RANK:'
            SELECT 
                PrebookRank,
                COUNT(*) as RecordCount,
                COUNT(DISTINCT prebooksupplier) as UniqueSuppliers,
                COUNT(DISTINCT Reservationid) as UniqueReservations,
                AVG(CAST(Profit AS FLOAT)) as AvgProfit,
                MIN(CAST(Profit AS FLOAT)) as MinProfit,
                MAX(CAST(Profit AS FLOAT)) as MaxProfit
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY PrebookRank
            ORDER BY PrebookRank;
            
            -- Show sample data
            PRINT ''
            PRINT 'SAMPLE DATA (Top 5):'
            SELECT TOP 5
                ReportId,
                Reservationid,
                PrebookRank,
                prebooksupplier,
                CAST(Profit AS DECIMAL(10,2)) as Profit,
                CAST(Createdate AS DATE) as CreateDate,
                PrimaryPrebookId
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            ORDER BY Reservationid, PrebookRank;
            
            -- Show supplier diversity
            PRINT ''
            PRINT 'SUPPLIER DIVERSITY:'
            SELECT 
                prebooksupplier,
                COUNT(*) as RecordCount,
                COUNT(DISTINCT Reservationid) as UniqueReservations
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY prebooksupplier
            ORDER BY RecordCount DESC;
            
        END
        ELSE
        BEGIN
            PRINT '⚠️ WARNING: No additional prebooks were created.'
            PRINT 'This could be normal if:'
            PRINT '   - No reservations have multiple prebook options'
            PRINT '   - All prebooks are from the same supplier'
            PRINT '   - Data doesn''t meet diversity criteria'
            PRINT '   - All bookings are already optimized'
            PRINT '   - No data created today'
        END
        
    END TRY
    BEGIN CATCH
        PRINT '❌ ERROR: ' + ERROR_MESSAGE()
        PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR(10))
        PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR(10))
        RETURN
    END CATCH
    
END
ELSE
BEGIN
    PRINT '⚠️ No source data found for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10))
    PRINT 'Please use a different RepricerId that has data'
END

PRINT ''
PRINT '=========================================='
PRINT 'TEST COMPLETED'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

-- Additional verification queries
PRINT ''
PRINT 'VERIFICATION QUERIES:'
PRINT '-- Check table structure --'
SELECT 
    COUNT(*) as TotalColumns,
    SUM(CASE WHEN COLUMN_NAME IN ('PrebookRank', 'PrimaryPrebookId') THEN 1 ELSE 0 END) as NewColumns
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ReservationReportDetailsAdditionalPrebook';

PRINT ''
PRINT '-- Check data integrity --'
SELECT 
    COUNT(*) as TotalRecords,
    COUNT(DISTINCT Reservationid) as UniqueReservations,
    COUNT(DISTINCT prebooksupplier) as UniqueSuppliers,
    MIN(PrebookRank) as MinRank,
    MAX(PrebookRank) as MaxRank,
    COUNT(CASE WHEN PrimaryPrebookId IS NOT NULL THEN 1 END) as RecordsWithPrimaryRef
FROM ReservationReportDetailsAdditionalPrebook
WHERE Repricerid = @TestRepricerId;

PRINT ''
PRINT 'Manual testing queries:'
PRINT '-- Compare with primary prebooks --'
PRINT 'SELECT p.Reservationid, p.prebooksupplier as Primary, a.PrebookRank, a.prebooksupplier as Additional'
PRINT 'FROM ReservationReportDetails p'
PRINT 'INNER JOIN ReservationReportDetailsAdditionalPrebook a ON p.Reservationid = a.Reservationid AND p.Repricerid = a.Repricerid'
PRINT 'WHERE p.Repricerid = ' + CAST(@TestRepricerId AS VARCHAR(10))
PRINT 'ORDER BY p.Reservationid, a.PrebookRank;'
