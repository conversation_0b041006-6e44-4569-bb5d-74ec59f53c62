-- =============================================
-- TEST ONE ROW PER SUPPLIER FOR ADDITIONAL PREBOOK FUNCTIONALITY
-- Author: Augment Agent
-- Date: 2025-01-27
-- Description: Verify only one row per supplier with max profit and best cancellation
-- =============================================

USE [rpndb]
GO

PRINT '=========================================='
PRINT 'TESTING ONE ROW PER SUPPLIER LOGIC'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

DECLARE @TestRepricerId INT = 1;

-- Clear existing test data
DELETE FROM ReservationReportDetailsAdditionalPrebook WHERE Repricerid = @TestRepricerId;
PRINT 'Cleared existing test data for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10))

-- Check source data
DECLARE @SourceCount INT;
SELECT @SourceCount = COUNT(*)
FROM ReservationTable 
WHERE RepricerID = @TestRepricerId;

PRINT 'Source data in ReservationTable: ' + CAST(@SourceCount AS VARCHAR(10)) + ' records'

IF @SourceCount > 0
BEGIN
    PRINT ''
    PRINT 'Executing stored procedure...'
    
    BEGIN TRY
        -- Execute the procedure
        EXEC dbo.usp_upd_reservationreport_AdditionalPrebook 
            @Repricerid = @TestRepricerId, 
            @Reservationid = NULL;
        
        DECLARE @ResultCount INT;
        SELECT @ResultCount = COUNT(*)
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId;
        
        PRINT '✅ Procedure executed successfully!'
        PRINT 'Additional prebooks created: ' + CAST(@ResultCount AS VARCHAR(10))
        
        IF @ResultCount > 0
        BEGIN
            PRINT ''
            PRINT '=========================================='
            PRINT 'ONE ROW PER SUPPLIER VERIFICATION'
            PRINT '=========================================='
            
            -- TEST 1: Check for duplicate suppliers per reservation
            PRINT 'TEST 1: Checking for duplicate suppliers per reservation...'
            
            SELECT 
                Reservationid,
                prebooksupplier,
                COUNT(*) as RowCount,
                'DUPLICATE SUPPLIER' as Issue
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY Reservationid, prebooksupplier
            HAVING COUNT(*) > 1
            ORDER BY Reservationid, prebooksupplier;
            
            IF @@ROWCOUNT = 0
            BEGIN
                PRINT '✅ PASS: No duplicate suppliers found per reservation'
            END
            ELSE
            BEGIN
                PRINT '❌ FAIL: Found duplicate suppliers per reservation!'
            END
            
            -- TEST 2: Verify we have the best profit per supplier
            PRINT ''
            PRINT 'TEST 2: Verifying best profit per supplier...'
            
            -- Check if there are better options in source data that weren't selected
            WITH SourceBestPerSupplier AS (
                SELECT 
                    rt.RepricerID,
                    rt.ReservationId,
                    rt.PrebookProviders as PrebookSupplier,
                    MAX(rt.Profit * rt.CurrencyFactortoEur) as MaxProfit,
                    MAX(rt.MatchedPreBookCancellationDate) as BestCancellationDate
                FROM ReservationTable rt
                WHERE rt.RepricerID = @TestRepricerId
                  AND CAST(rt.createdate AS DATE) = CAST(GETDATE() AS DATE)
                  AND ISNULL(rt.IsOptimized, 0) = 0
                  AND rt.cPStatus = 'loose'
                  AND rt.Providers <> rt.PrebookProviders
                GROUP BY rt.RepricerID, rt.ReservationId, rt.PrebookProviders
            ),
            SelectedOptions AS (
                SELECT 
                    Repricerid,
                    Reservationid,
                    prebooksupplier,
                    Profit,
                    MatchedPreBookCancellationDate
                FROM ReservationReportDetailsAdditionalPrebook
                WHERE Repricerid = @TestRepricerId
            )
            SELECT 
                s.ReservationId,
                s.PrebookSupplier,
                s.MaxProfit as SourceMaxProfit,
                ISNULL(sel.Profit, 0) as SelectedProfit,
                CASE 
                    WHEN sel.Profit < s.MaxProfit THEN 'SUBOPTIMAL SELECTION'
                    ELSE 'OPTIMAL'
                END as ProfitCheck
            FROM SourceBestPerSupplier s
            LEFT JOIN SelectedOptions sel 
                ON s.ReservationId = sel.Reservationid 
                AND s.PrebookSupplier = sel.prebooksupplier
            WHERE sel.Profit IS NULL OR sel.Profit < s.MaxProfit
            ORDER BY s.ReservationId, s.PrebookSupplier;
            
            IF @@ROWCOUNT = 0
            BEGIN
                PRINT '✅ PASS: All selected options have optimal profit per supplier'
            END
            ELSE
            BEGIN
                PRINT '⚠️ WARNING: Some suboptimal profit selections found'
            END
            
            -- TEST 3: Summary by reservation
            PRINT ''
            PRINT 'TEST 3: Summary by reservation...'
            
            SELECT 
                Reservationid,
                COUNT(*) as SupplierCount,
                COUNT(DISTINCT prebooksupplier) as UniqueSuppliers,
                STRING_AGG(prebooksupplier, ', ') as Suppliers,
                STRING_AGG(CAST(PrebookRank AS VARCHAR), ', ') as Ranks,
                AVG(CAST(Profit AS FLOAT)) as AvgProfit,
                MAX(CAST(Profit AS FLOAT)) as MaxProfit,
                MIN(CAST(Profit AS FLOAT)) as MinProfit
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY Reservationid
            ORDER BY Reservationid;
            
            -- TEST 4: Detailed view of selected options
            PRINT ''
            PRINT 'TEST 4: Detailed view of selected options...'
            
            SELECT 
                Reservationid,
                PrebookRank,
                prebooksupplier,
                CAST(Profit AS DECIMAL(10,2)) as Profit,
                CAST(MatchedPreBookCancellationDate AS DATE) as CancellationDate,
                CAST(Createdate AS DATE) as CreateDate,
                PreBookId
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            ORDER BY Reservationid, PrebookRank;
            
            -- TEST 5: Verify rank sequence
            PRINT ''
            PRINT 'TEST 5: Verifying rank sequence...'
            
            SELECT 
                Reservationid,
                COUNT(*) as PrebookCount,
                MIN(PrebookRank) as MinRank,
                MAX(PrebookRank) as MaxRank,
                STRING_AGG(CAST(PrebookRank AS VARCHAR), ',') as RankSequence,
                CASE 
                    WHEN MIN(PrebookRank) = 2 AND MAX(PrebookRank) <= 3 THEN 'VALID'
                    ELSE 'INVALID'
                END as RankValidation
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY Reservationid
            ORDER BY Reservationid;
            
            -- TEST 6: Compare with primary prebooks
            PRINT ''
            PRINT 'TEST 6: Comparing with primary prebooks...'
            
            SELECT 
                p.Reservationid,
                p.prebooksupplier as PrimarySupplier,
                CAST(p.Profit AS DECIMAL(10,2)) as PrimaryProfit,
                a.PrebookRank,
                a.prebooksupplier as AdditionalSupplier,
                CAST(a.Profit AS DECIMAL(10,2)) as AdditionalProfit,
                CASE 
                    WHEN a.prebooksupplier = p.prebooksupplier THEN 'SAME SUPPLIER (ERROR)'
                    WHEN a.Profit > p.Profit THEN 'BETTER PROFIT'
                    WHEN a.Profit = p.Profit THEN 'EQUAL PROFIT'
                    ELSE 'LOWER PROFIT'
                END as Comparison
            FROM ReservationReportDetails p
            INNER JOIN ReservationReportDetailsAdditionalPrebook a
                ON p.Reservationid = a.Reservationid AND p.Repricerid = a.Repricerid
            WHERE p.Repricerid = @TestRepricerId
            ORDER BY p.Reservationid, a.PrebookRank;
            
        END
        ELSE
        BEGIN
            PRINT '⚠️ WARNING: No additional prebooks were created.'
            PRINT 'Analyzing source data...'
            
            -- Debug: Check source data characteristics
            SELECT 
                COUNT(*) as TotalRecords,
                COUNT(DISTINCT PrebookProviders) as UniqueSuppliers,
                COUNT(DISTINCT ReservationId) as UniqueReservations,
                SUM(CASE WHEN CAST(createdate AS DATE) = CAST(GETDATE() AS DATE) THEN 1 ELSE 0 END) as TodayRecords,
                SUM(CASE WHEN cPStatus = 'loose' THEN 1 ELSE 0 END) as LooseRecords,
                SUM(CASE WHEN ISNULL(IsOptimized, 0) = 0 THEN 1 ELSE 0 END) as NotOptimizedRecords,
                SUM(CASE WHEN Providers <> PrebookProviders THEN 1 ELSE 0 END) as DifferentSupplierRecords
            FROM ReservationTable
            WHERE RepricerID = @TestRepricerId;
        END
        
    END TRY
    BEGIN CATCH
        PRINT '❌ ERROR: ' + ERROR_MESSAGE()
        PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR(10))
        PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR(10))
    END CATCH
    
END
ELSE
BEGIN
    PRINT '⚠️ No source data found for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10))
    PRINT 'Please use a different RepricerId that has data'
END

PRINT ''
PRINT '=========================================='
PRINT 'ONE ROW PER SUPPLIER TEST COMPLETED'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='
