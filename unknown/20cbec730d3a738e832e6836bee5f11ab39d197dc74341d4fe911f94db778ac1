-- =============================================
-- WORKING VERSION: Additional Prebook Stored Procedure
-- Author: Augment Agent
-- Date: 2025-01-27
-- Description: ONE ROW PER SUPPLIER with MAX PROFIT and BEST CANCELLATION
-- Requirements: For each prebookSupplier, only 1 row with highest profit and best cancellation policy
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[usp_upd_reservationreport_AdditionalPrebook_WORKING]
(
    @Repricerid    INT,
    @Reservationid INT = NULL
)
AS
BEGIN
    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    DECLARE @CurrentDate DATE = CAST(GETDATE() AS DATE);

    PRINT 'Starting Additional Prebook procedure for RepricerId: ' + CAST(@Repricerid AS VARCHAR(10));

    -- STEP 1: Clear existing data for this repricer to prevent duplicates
    DELETE FROM ReservationReportDetailsAdditionalPrebook 
    WHERE Repricerid = @Repricerid 
      AND (@Reservationid IS NULL OR Reservationid = @Reservationid);

    PRINT 'Cleared existing data';

    -- STEP 2: Get source data from ReservationTable only (no ReservationTablelog needed)
    WITH SourceData AS (
        SELECT 
            rt.id,
            rt.ReservationId,
            rt.createdate,
            rt.RepricerID,
            rt.Reservationadultcount,
            rt.Prebookadultcount,
            rt.Reservationchildages,
            rt.Prebookchildages,
            rt.Providers,
            rt.BookingDate,
            (rt.ReservationPrice * rt.CurrencyFactortoEur) as ReservationPrice,
            (rt.PreBookPrice * rt.CurrencyFactortoEur) as PreBookPrice,
            (rt.ProfitAfterCancellation * rt.CurrencyFactortoEur) as ProfitAfterCancellation,
            (rt.Profit * rt.CurrencyFactortoEur) as Profit,
            rt.CurrencyFactortoEur,
            rt.Reservationroomname,
            rt.PreBookRoomName,
            rt.ReservationRoomBoard,
            rt.PreBookRoomBoard,
            rt.ReservationRoomInfo,
            rt.PrebookRoomInfo,
            rt.PreBookRoomIndex,
            rt.MatchedReservationCancellationDate,
            rt.MatchedPreBookCancellationDate,
            rt.MatchedReservationCancellationChargeByPolicy,
            rt.MatchedPreBookCancellationChargeByPolicy,
            rt.IsCancellationPolicyMatched,
            rt.cPStatus,
            rt.cpdaysgain,
            (rt.matchedcancellationpolicygain * rt.CurrencyFactortoEur) as matchedcancellationpolicygain,
            rt.token,
            rt.AvailabilityToken,
            rt.PrebookProviders as PrebookSupplier,
            ((dbo.ExtractValue(rt.matchedreservationcancellationchargebypolicy)) * rt.CurrencyFactortoEur) as MatchedReservationCancellationChargeByPolicyToEur,
            ((dbo.ExtractValue(rt.MatchedPreBookCancellationChargeByPolicy)) * rt.CurrencyFactortoEur) as MatchedPreBookCancellationChargeByPolicytoEur,
            LEFT(ISNULL(rt.ReservationGiataMappingId, ''), CASE
                WHEN CHARINDEX(',', ISNULL(rt.ReservationGiataMappingId, '')) > 0 THEN
                    CHARINDEX(',', ISNULL(rt.ReservationGiataMappingId, '')) - 1
                ELSE LEN(ISNULL(rt.ReservationGiataMappingId, ''))
            END) as ReservationGiataMappingId,
            LEFT(ISNULL(rt.SearchGiataMappingId, ''), CASE
                WHEN CHARINDEX(',', ISNULL(rt.SearchGiataMappingId, '')) > 0 THEN
                    CHARINDEX(',', ISNULL(rt.SearchGiataMappingId, '')) - 1
                ELSE LEN(ISNULL(rt.SearchGiataMappingId, ''))
            END) as SearchGiataMappingId,
            rt.PreBookGiataPropertyName,
            rt.ReservationGiataPropertyName,
            rt.ReservationRoomBoardGroup,
            rt.PrebookRoomBoardGroup,
            rt.ReservationCancellationType,
            rt.PreBookCancellationType,
            rt.CancellationPolicyRemark,
            rt.NumberOfRooms,
            rt.ReservationStatus,
            rt.checkin,
            rt.checkout,
            rt.reservationhotelname,
            rt.prebookhotelname,
            rt.prebookdestination,
            rt.reservationdestination,
            rt.roomType,
            rt.createdate as firstcreatedate,
            rt.DiffDays_Optimisation,
            rt.PriceDifferenceValue,
            rt.PriceDifferencePercentage,
            rt.pricedifferencecurrency,
            rt.IsUsePercentage,
            rt.traveldaysmaxsearchindays,
            rt.traveldaysminsearchindays,
            rt.ResellerName,
            rt.ResellerCode,
            rt.ResellerType,
            ISNULL(rt.IsOptimized, 0) as IsOptimized,
            ISNULL(bat.NewBookingId, 0) as bat_NewBookingId
        FROM ReservationTable rt
        LEFT JOIN BookingActionsTaken bat ON rt.ReservationId = bat.ReservationId AND rt.RepricerID = bat.RepricerId AND bat.ActionId = 1
        WHERE rt.RepricerID = @Repricerid
          AND (@Reservationid IS NULL OR rt.ReservationId = @Reservationid)
          AND CAST(rt.createdate AS DATE) = @CurrentDate  -- SAME DAY REQUIREMENT
          AND ISNULL(rt.IsOptimized, 0) = 0               -- EXCLUDE ALREADY OPTIMIZED BOOKINGS
          AND ISNULL(bat.NewBookingId, 0) = 0             -- EXCLUDE OPTIMIZED: BookingActionsTaken.ActionId=1 means optimized
          AND rt.cPStatus = 'loose'                       -- ONLY LOOSE CANCELLATION POLICY
          AND rt.Providers <> rt.PrebookProviders         -- DIFFERENT SUPPLIER FROM PRIMARY
    ),
    
    -- STEP 3: Get PRIMARY prebook characteristics for comparison
    PrimaryPrebooks AS (
        SELECT 
            rt.RepricerID,
            rt.ReservationId,
            rt.PrebookProviders as PrimarySupplier,
            rt.MatchedPreBookCancellationDate as PrimaryCancellationDate,
            rt.id as PrimaryPrebookId
        FROM ReservationTable rt
        LEFT JOIN BookingActionsTaken bat ON rt.ReservationId = bat.ReservationId AND rt.RepricerID = bat.RepricerId AND bat.ActionId = 1
        WHERE rt.RepricerID = @Repricerid
          AND (@Reservationid IS NULL OR rt.ReservationId = @Reservationid)
          AND ISNULL(rt.IsOptimized, 0) = 0
          AND ISNULL(bat.NewBookingId, 0) = 0
          AND rt.cPStatus = 'loose'
          AND ROW_NUMBER() OVER (
              PARTITION BY rt.RepricerID, rt.ReservationID
              ORDER BY 
                  CAST(rt.CreateDate AS DATE) DESC,
                  rt.Profit DESC,
                  rt.CreateDate DESC
          ) = 1  -- GET PRIMARY (RANK 1) PREBOOK
    ),
    
    -- STEP 4: Get BEST option per supplier (ONE ROW PER SUPPLIER with MAX PROFIT and BEST CANCELLATION)
    BestPerSupplier AS (
        SELECT 
            s.*,
            p.PrimarySupplier,
            p.PrimaryCancellationDate,
            p.PrimaryPrebookId,
            ROW_NUMBER() OVER (
                PARTITION BY s.RepricerID, s.ReservationId, s.PrebookSupplier
                ORDER BY
                    s.Profit DESC,                              -- 1st: HIGHEST PROFIT for this supplier
                    s.MatchedPreBookCancellationDate DESC,      -- 2nd: BETTER (later) cancellation date
                    s.createdate DESC                           -- 3rd: Most recent
            ) as supplier_rank
        FROM SourceData s
        INNER JOIN PrimaryPrebooks p ON s.RepricerID = p.RepricerID AND s.ReservationId = p.ReservationId
        WHERE s.PrebookSupplier != p.PrimarySupplier  -- ENSURE DIFFERENT FROM PRIMARY SUPPLIER
    ),
    
    -- STEP 5: Rank suppliers by quality (assign ranks 2, 3)
    RankedSuppliers AS (
        SELECT 
            *,
            ROW_NUMBER() OVER (
                PARTITION BY RepricerID, ReservationId
                ORDER BY
                    Profit DESC,                              -- 1st: HIGHEST PROFIT overall
                    MatchedPreBookCancellationDate DESC,      -- 2nd: BETTER cancellation date
                    createdate DESC                           -- 3rd: Most recent
            ) + 1 as PrebookRank  -- +1 to start from rank 2 (rank 1 is primary)
        FROM BestPerSupplier
        WHERE supplier_rank = 1  -- ONLY THE BEST OPTION PER SUPPLIER
    )
    
    -- STEP 6: Insert into target table (only ranks 2 and 3)
    INSERT INTO ReservationReportDetailsAdditionalPrebook (
        Reservationid, Repricerid, Createdate, Reservationadultcount, Prebookadultcount,
        Reservationchildages, PrebookChildAges, Providers, BookingDate, ReservationPrice,
        PreBookPrice, ProfitAfterCancellation, Profit, CurrencyFactorToEur, ReservationRoomName,
        PreBookRoomName, ReservationRoomBoard, PreBookRoomBoard, ReservationRoomInfo, PrebookRoomInfo,
        PreBookRoomIndex, MatchedReservationCancellationDate, MatchedPreBookCancellationDate,
        MatchedReservationCancellationChargeByPolicy, MatchedPreBookCancellationChargeByPolicy,
        IsCancellationPolicyMatched, CPStatus, CPDaysGain, MatchedCancellationPolicyGain, Token,
        AvailabilityToken, NumberOfRooms, ReservationStatus, prebooksupplier, checkin, checkout,
        reservationhotelname, prebookhotelname, prebookdestination, reservationdestination, roomType,
        UpdatedOn, DiffDays_Optimisation, PriceDifferenceValue, PriceDifferencePercentage,
        pricedifferencecurrency, IsUsePercentage, traveldaysmaxsearchindays, traveldaysminsearchindays,
        MatchedReservationCancellationChargeByPolicyToEur, MatchedPreBookCancellationChargeByPolicytoEur,
        ReservationGiataMappingId, SearchGiataMappingId, PreBookGiataPropertyName, ReservationGiataPropertyName,
        ReservationRoomBoardGroup, PrebookRoomBoardGroup, ReservationCancellationType, PreBookCancellationType,
        CancellationPolicyRemark, ResellerName, ResellerCode, ResellerType, PreBookId, PrebookRank, PrimaryPrebookId
    )
    SELECT 
        ReservationId, RepricerID, firstcreatedate, Reservationadultcount, Prebookadultcount,
        Reservationchildages, Prebookchildages, Providers, BookingDate, ReservationPrice,
        PreBookPrice, ProfitAfterCancellation, Profit, CurrencyFactortoEur, Reservationroomname,
        PreBookRoomName, ReservationRoomBoard, PreBookRoomBoard, ReservationRoomInfo, PrebookRoomInfo,
        PreBookRoomIndex, MatchedReservationCancellationDate, MatchedPreBookCancellationDate,
        MatchedReservationCancellationChargeByPolicy, MatchedPreBookCancellationChargeByPolicy,
        IsCancellationPolicyMatched, cPStatus, cpdaysgain, matchedcancellationpolicygain, token,
        AvailabilityToken, NumberOfRooms, ReservationStatus, PrebookSupplier, checkin, checkout,
        reservationhotelname, prebookhotelname, prebookdestination, reservationdestination, roomType,
        GETDATE(), DiffDays_Optimisation, PriceDifferenceValue, PriceDifferencePercentage,
        pricedifferencecurrency, IsUsePercentage, traveldaysmaxsearchindays, traveldaysminsearchindays,
        MatchedReservationCancellationChargeByPolicyToEur, MatchedPreBookCancellationChargeByPolicytoEur,
        ReservationGiataMappingId, SearchGiataMappingId, PreBookGiataPropertyName, ReservationGiataPropertyName,
        ReservationRoomBoardGroup, PrebookRoomBoardGroup, ReservationCancellationType, PreBookCancellationType,
        CancellationPolicyRemark, ResellerName, ResellerCode, ResellerType, id, PrebookRank, PrimaryPrebookId
    FROM RankedSuppliers
    WHERE PrebookRank BETWEEN 2 AND 3;  -- ONLY RANKS 2 AND 3

    DECLARE @InsertedCount INT = @@ROWCOUNT;
    PRINT 'Inserted ' + CAST(@InsertedCount AS VARCHAR(10)) + ' additional prebook records';

END
