-- =============================================
-- TEST RANK LOGIC FOR ADDITIONAL PREBOOK FUNCTIONALITY
-- Author: Augment Agent
-- Date: 2025-01-27
-- Description: Verify that rank logic is working correctly
-- =============================================

USE [rpndb]
GO

PRINT '=========================================='
PRINT 'TESTING RANK LOGIC FOR ADDITIONAL PREBOOKS'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

DECLARE @TestRepricerId INT = 1;

-- Clear existing test data
DELETE FROM ReservationReportDetailsAdditionalPrebook WHERE Repricerid = @TestRepricerId;
PRINT 'Cleared existing test data for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10))

-- Check source data
DECLARE @SourceCount INT;
SELECT @SourceCount = COUNT(*)
FROM ReservationTable 
WHERE RepricerID = @TestRepricerId;

PRINT 'Source data in ReservationTable: ' + CAST(@SourceCount AS VARCHAR(10)) + ' records'

IF @SourceCount = 0
BEGIN
    SELECT @SourceCount = COUNT(*)
    FROM ReservationTablelog 
    WHERE RepricerID = @TestRepricerId;
    PRINT 'Source data in ReservationTablelog: ' + CAST(@SourceCount AS VARCHAR(10)) + ' records'
END

IF @SourceCount > 0
BEGIN
    PRINT ''
    PRINT 'Executing stored procedure to test rank logic...'
    
    BEGIN TRY
        -- Execute the procedure
        EXEC dbo.usp_upd_reservationreport_AdditionalPrebook 
            @Repricerid = @TestRepricerId, 
            @Reservationid = NULL;
        
        PRINT '✅ Procedure executed successfully!'
        
        -- Check results
        DECLARE @ResultCount INT;
        SELECT @ResultCount = COUNT(*)
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId;
        
        PRINT 'Additional prebooks created: ' + CAST(@ResultCount AS VARCHAR(10))
        
        IF @ResultCount > 0
        BEGIN
            PRINT ''
            PRINT '✅ SUCCESS: Additional prebooks were created!'
            
            -- RANK LOGIC VERIFICATION
            PRINT ''
            PRINT '=========================================='
            PRINT 'RANK LOGIC VERIFICATION'
            PRINT '=========================================='
            
            -- Test 1: Check if ranks are sequential (2, 3) per reservation
            PRINT 'TEST 1: Checking rank distribution...'
            SELECT 
                PrebookRank,
                COUNT(*) as RecordCount,
                COUNT(DISTINCT Reservationid) as UniqueReservations
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY PrebookRank
            ORDER BY PrebookRank;
            
            -- Test 2: Check for proper rank sequence per reservation
            PRINT ''
            PRINT 'TEST 2: Checking rank sequence per reservation...'
            SELECT 
                Reservationid,
                COUNT(*) as PrebookCount,
                MIN(PrebookRank) as MinRank,
                MAX(PrebookRank) as MaxRank,
                STRING_AGG(CAST(PrebookRank AS VARCHAR), ',') as RankSequence
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY Reservationid
            ORDER BY Reservationid;
            
            -- Test 3: Check supplier diversity
            PRINT ''
            PRINT 'TEST 3: Checking supplier diversity per reservation...'
            SELECT 
                Reservationid,
                COUNT(DISTINCT prebooksupplier) as UniqueSuppliers,
                STRING_AGG(prebooksupplier, ', ') as Suppliers
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            GROUP BY Reservationid
            HAVING COUNT(*) > 1  -- Only show reservations with multiple prebooks
            ORDER BY Reservationid;
            
            -- Test 4: Detailed view of rank logic
            PRINT ''
            PRINT 'TEST 4: Detailed rank analysis (Top 10)...'
            SELECT TOP 10
                Reservationid,
                PrebookRank,
                prebooksupplier,
                CAST(Profit AS DECIMAL(10,2)) as Profit,
                CAST(Createdate AS DATE) as CreateDate,
                PrimaryPrebookId
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId
            ORDER BY Reservationid, PrebookRank;
            
            -- Test 5: Check for rank gaps or issues
            PRINT ''
            PRINT 'TEST 5: Checking for rank logic issues...'
            
            -- Check for rank 1 (should not exist in additional prebooks)
            DECLARE @Rank1Count INT;
            SELECT @Rank1Count = COUNT(*)
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId AND PrebookRank = 1;
            
            IF @Rank1Count > 0
            BEGIN
                PRINT '❌ ERROR: Found ' + CAST(@Rank1Count AS VARCHAR(10)) + ' records with rank 1 (should be 0)'
            END
            ELSE
            BEGIN
                PRINT '✅ PASS: No rank 1 records found (correct)'
            END
            
            -- Check for ranks > 3 (should not exist with current logic)
            DECLARE @HighRankCount INT;
            SELECT @HighRankCount = COUNT(*)
            FROM ReservationReportDetailsAdditionalPrebook
            WHERE Repricerid = @TestRepricerId AND PrebookRank > 3;
            
            IF @HighRankCount > 0
            BEGIN
                PRINT '⚠️ WARNING: Found ' + CAST(@HighRankCount AS VARCHAR(10)) + ' records with rank > 3'
            END
            ELSE
            BEGIN
                PRINT '✅ PASS: No ranks > 3 found (correct for current logic)'
            END
            
            -- Check for missing rank 2 when rank 3 exists
            PRINT ''
            PRINT 'TEST 6: Checking rank sequence integrity...'
            SELECT 
                r.Reservationid,
                'Missing Rank 2' as Issue
            FROM (
                SELECT DISTINCT Reservationid 
                FROM ReservationReportDetailsAdditionalPrebook 
                WHERE Repricerid = @TestRepricerId AND PrebookRank = 3
            ) r
            WHERE NOT EXISTS (
                SELECT 1 
                FROM ReservationReportDetailsAdditionalPrebook 
                WHERE Repricerid = @TestRepricerId 
                  AND PrebookRank = 2 
                  AND Reservationid = r.Reservationid
            );
            
            IF @@ROWCOUNT = 0
            BEGIN
                PRINT '✅ PASS: All rank sequences are valid'
            END
            
        END
        ELSE
        BEGIN
            PRINT '⚠️ WARNING: No additional prebooks were created.'
            PRINT 'Possible reasons:'
            PRINT '   - No reservations have multiple prebook options'
            PRINT '   - All prebooks are from the same supplier'
            PRINT '   - Data doesn''t meet diversity criteria'
            PRINT '   - All bookings are already optimized'
            PRINT '   - No data created today'
            
            -- Debug: Check source data characteristics
            PRINT ''
            PRINT 'DEBUG: Source data analysis...'
            SELECT 
                COUNT(*) as TotalRecords,
                COUNT(DISTINCT PrebookProviders) as UniqueSuppliers,
                COUNT(DISTINCT ReservationId) as UniqueReservations,
                SUM(CASE WHEN CAST(createdate AS DATE) = CAST(GETDATE() AS DATE) THEN 1 ELSE 0 END) as TodayRecords,
                SUM(CASE WHEN cPStatus = 'loose' THEN 1 ELSE 0 END) as LooseRecords,
                SUM(CASE WHEN ISNULL(IsOptimized, 0) = 0 THEN 1 ELSE 0 END) as NotOptimizedRecords
            FROM ReservationTable
            WHERE RepricerID = @TestRepricerId;
        END
        
    END TRY
    BEGIN CATCH
        PRINT '❌ ERROR: ' + ERROR_MESSAGE()
        PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR(10))
        PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR(10))
    END CATCH
    
END
ELSE
BEGIN
    PRINT '⚠️ No source data found for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10))
    PRINT 'Please use a different RepricerId that has data'
END

PRINT ''
PRINT '=========================================='
PRINT 'RANK LOGIC TEST COMPLETED'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='
