-- =============================================
-- Author: Augment Agent
-- Create date: 2025-01-27
-- Description: Get additional prebook options (ranks 2-3) from ReservationReportDetailsAdditionalPrebook table
-- Usage: EXEC usp_get_AdditionalPrebookOptions @RepricerId = 99
-- =============================================
CREATE PROCEDURE [dbo].[usp_get_AdditionalPrebookOptions]
    @RepricerId INT,
    @ReservationId VARCHAR(MAX) = NULL,
    @FromDate VARCHAR(20) = NULL,
    @ToDate VARCHAR(20) = NULL,
    @PageSize INT = NULL,
    @PageNumber INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    DECLARE @fromDateVar DATE;
    DECLARE @ToDateVar DATE;

    -- Parse date parameters
    IF @FromDate IS NOT NULL AND @FromDate != ''
    BEGIN
        SET @fromDateVar = CAST(@FromDate AS DATE);
    END

    IF @ToDate IS NOT NULL AND @ToDate != ''
    BEGIN
        SET @ToDateVar = CAST(@ToDate AS DATE);
    END

    -- Create temp table for reservation IDs if provided
    CREATE TABLE #TempReservationIds (ReservationId INT);
    
    IF @ReservationId IS NOT NULL AND @ReservationId != ''
    BEGIN
        INSERT INTO #TempReservationIds (ReservationId)
        SELECT CAST(value AS INT)
        FROM STRING_SPLIT(@ReservationId, ',')
        WHERE value != '';
    END

    -- Main query to get additional prebook options
    -- Uses same structure as primary prebook query but from ReservationReportDetailsAdditionalPrebook table
    SELECT 
        rrd.Reservationid,
        rrd.Repricerid,
        rrd.Createdate,
        rrd.Reservationadultcount,
        rrd.Prebookadultcount,
        rrd.Reservationchildages,
        rrd.PrebookChildAges,
        rrd.Providers,
        rrd.BookingDate,
        rrd.ReservationPrice,
        rrd.PreBookPrice,
        rrd.ProfitAfterCancellation,
        rrd.Profit,
        rrd.CurrencyFactorToEur,
        rrd.ReservationRoomName,
        rrd.PreBookRoomName,
        rrd.ReservationRoomBoard,
        rrd.PreBookRoomBoard,
        rrd.ReservationRoomInfo,
        rrd.PrebookRoomInfo,
        rrd.PreBookRoomIndex,
        rrd.MatchedReservationCancellationDate,
        rrd.MatchedPreBookCancellationDate,
        rrd.MatchedReservationCancellationChargeByPolicy,
        rrd.MatchedPreBookCancellationChargeByPolicy,
        rrd.IsCancellationPolicyMatched,
        rrd.CPStatus,
        rrd.CPDaysGain,
        rrd.MatchedCancellationPolicyGain,
        rrd.Token,
        rrd.AvailabilityToken,
        rrd.NumberOfRooms,
        rrd.ReservationStatus,
        rrd.prebooksupplier,
        rrd.checkin,
        rrd.checkout,
        rrd.reservationhotelname,
        rrd.prebookhotelname,
        rrd.prebookdestination,
        rrd.reservationdestination,
        rrd.roomType,
        rrd.UpdatedOn,
        rrd.DiffDays_Optimisation,
        rrd.PriceDifferenceValue,
        rrd.PriceDifferencePercentage,
        rrd.pricedifferencecurrency,
        rrd.IsUsePercentage,
        rrd.traveldaysmaxsearchindays,
        rrd.traveldaysminsearchindays,
        rrd.MatchedReservationCancellationChargeByPolicyToEur,
        rrd.MatchedPreBookCancellationChargeByPolicytoEur,
        rrd.ReservationGiataMappingId,
        rrd.SearchGiataMappingId,
        rrd.PreBookGiataPropertyName,
        rrd.ReservationGiataPropertyName,
        rrd.ReservationRoomBoardGroup,
        rrd.PrebookRoomBoardGroup,
        rrd.ReservationCancellationType,
        rrd.PreBookCancellationType,
        rrd.CancellationPolicyRemark,
        rrd.ResellerName,
        rrd.ResellerCode,
        rrd.ResellerType,
        rrd.PrebookRank,
        -- Additional columns for compatibility with existing data mapping
        'Prebook' as reportType,
        '' as NewReservationStatus,
        0 as NewReservationID,
        rrd.UpdatedOn as LastActionTakenDate,
        CASE WHEN EXISTS (
            SELECT 1 FROM BookingActionsTaken bat 
            WHERE bat.RepricerId = rrd.Repricerid 
            AND bat.ReservationId = rrd.Reservationid
        ) THEN 1 ELSE 0 END as IsBookingActionTaken,
        8 as ActionId, -- Default action ID for no action taken
        rrd.pricedifferencecurrency as PreBookCurrency,
        rrd.pricedifferencecurrency as ReservationCurrency,
        0.0 as CustomerAmount,
        rrd.pricedifferencecurrency as CustomerCurrency,
        1.0 as CustomerCurrencyFactor,
        rrd.checkin as reservationcheckin,
        rrd.checkout as reservationcheckout,
        rrd.Providers as reservationSupplier,
        rrd.reservationhotelname as reservationhotelname,
        rrd.ReservationRoomName as reservationroomname,
        rrd.ReservationRoomBoard as reservationroomboard,
        rrd.ReservationRoomInfo as reservationroominfo,
        rrd.ReservationRoomBoardGroup,
        rrd.ReservationCancellationType,
        rrd.ReservationGiataMappingId,
        rrd.ReservationGiataPropertyName,
        rrd.prebookhotelname as prebookhotelname,
        rrd.PreBookRoomName as prebookroomname,
        rrd.PreBookRoomBoard as prebookroomboard,
        rrd.PrebookRoomInfo,
        rrd.prebookdestination,
        rrd.PrebookRoomBoardGroup,
        rrd.PreBookCancellationType,
        rrd.SearchGiataMappingId,
        rrd.PreBookGiataPropertyName as SearchGiataPropertyName,
        rrd.UpdatedOn as ReservationLastActivity,
        rrd.UpdatedOn as PrebookLastActivity,
        NULL as ReservationCancelledOnDate,
        NULL as PrebookCancelledOnDate,
        NULL as ReservationCpJSON,
        NULL as PrebookCpJSON
    FROM dbo.ReservationReportDetailsAdditionalPrebook rrd
    WHERE rrd.Repricerid = @RepricerId
        AND (@ReservationId IS NULL OR rrd.Reservationid IN (SELECT ReservationId FROM #TempReservationIds))
        AND (@fromDateVar IS NULL OR CAST(rrd.Createdate AS DATE) >= @fromDateVar)
        AND (@ToDateVar IS NULL OR CAST(rrd.Createdate AS DATE) <= @ToDateVar)
        AND rrd.PrebookRank IN (2, 3) -- Only get ranks 2 and 3
    ORDER BY rrd.Reservationid, rrd.PrebookRank;

    -- Clean up
    DROP TABLE #TempReservationIds;
END
