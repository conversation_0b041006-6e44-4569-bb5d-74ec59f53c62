<template>
    <div class="card">
        <Timeline :value="events">
            <template #content="slotProps">
                <small class="p-text-secondary d-inline-block" style="width:150px">
                    {{slotProps.item.status}}
                </small>
            </template>
        </Timeline>
    </div>
</template>
<script>
import Button from 'primevue/button';
import Card from 'primevue/card';
import Divider from 'primevue/divider';
import Menu from 'primevue/menu';
import Skeleton from 'primevue/skeleton';
import Timeline from 'primevue/timeline';

export default {
    name: "TimeLine",
    components: {Card, Button, Divider, Menu, Skeleton, Timeline},
    data() {
        return {
            
        };
    },
    props: ['summary'],
    watch: {},
    computed: {
    },
    async mounted() {
    },
    methods: {
    }
}

</script>
