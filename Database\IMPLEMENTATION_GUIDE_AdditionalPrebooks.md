# 🎯 IMPLEMENTATION GUIDE: Additional Prebook Selection for "Active" Tab

## **📋 OVERVIEW**

This implementation adds support for displaying **3 most recent prebook options from different suppliers** created on the same day, instead of just one prebook option per reservation in the "Active" tab.

### **🔄 CURRENT vs NEW ARCHITECTURE**

#### **CURRENT (Single Prebook)**
```
ReservationTable → usp_upd_reservationreport → ReservationReportDetails → vw_ResevationReports → API
                   (Selects TOP 1)              (Stores 1 prebook)
```

#### **NEW (Multiple Prebooks)**
```
ReservationTable → usp_upd_reservationreport → ReservationReportDetails → vw_ResevationReports → API
                   (Selects TOP 1)              (Stores primary prebook)

ReservationTable → usp_upd_reservationreport_AdditionalPrebook → ReservationReportDetailsAdditionalPrebook
                   (Selects RANKS 2-3)                           (Stores additional prebooks)
```

---

## **🗄️ DATABASE CHANGES**

### **1. New Table: `ReservationReportDetailsAdditionalPrebook`**

**Purpose**: Store 2nd and 3rd ranked prebook options for each reservation

**Key Columns**:
- `PrebookRank` (INT): Ranking (2, 3, 4...)
- `PrimaryPrebookId` (BIGINT): Reference to primary prebook in `ReservationReportDetails`
- All other columns identical to `ReservationReportDetails`

**Indexes**:
- Primary key on `ReportId`
- Composite indexes on `RepricerId + CreateDate`, `ReservationId + RepricerId`
- Index on `PrimaryPrebookId` for joins

### **2. New Stored Procedure: `usp_upd_reservationreport_AdditionalPrebook`**

**Purpose**: Select and store additional prebook options using same business logic as original procedure

**Key Logic**:
```sql
-- CRITICAL SELECTION LOGIC (Same as original but ranks 2-3)
SELECT ROW_NUMBER() OVER (
    PARTITION BY RepricerId, ReservationID
    ORDER BY
        ISNULL(IsOptimized, 0) DESC,           -- 1st Priority: Already optimized
        CAST(CreateDate AS DATE) DESC,         -- 2nd Priority: Most recent date
        CASE
            WHEN (cpstatus = 'loose')
                AND ReservationGiataMappingId IS NULL
                AND CAST(CreateDate AS DATE) = @CurrentDate THEN 1
            WHEN (cpstatus = 'loose')
                AND ReservationGiataMappingId IS NOT NULL
                AND CAST(CreateDate AS DATE) = @CurrentDate THEN 2
            ELSE 3
        END,                                   -- 3rd Priority: CP status + mapping
        CAST(CreateDate AS DATE) DESC,         -- 4th Priority: Date again
        Profit DESC,                           -- 5th Priority: Highest profit
        CreateDate DESC                        -- 6th Priority: Most recent time
) as rn
FROM #temp_ActiveTab
WHERE CAST(CreateDate AS DATE) = @CurrentDate  -- SAME DAY REQUIREMENT
WHERE rn BETWEEN 2 AND 3;  -- SELECT RANKS 2 AND 3 (ADDITIONAL PREBOOKS)
```

**Key Features**:
- **Same Day Filter**: Only prebooks created on current date (`@CurrentDate`)
- **Identical Business Logic**: Uses exact same ranking criteria as original procedure
- **Different Suppliers**: Naturally selects different suppliers due to ranking
- **Reference Linking**: Links to primary prebook via `PrimaryPrebookId`
- **Automatic Execution**: Called automatically by `usp_upd_reservationreport` procedure

### **3. Database-Level Integration**

**Purpose**: Automatically execute additional prebook procedure from main procedure

**Implementation in `usp_upd_reservationreport`**:
```sql
-- At the end of usp_upd_reservationreport procedure
BEGIN TRY
    exec dbo.usp_upd_reservationreport_AdditionalPrebook @Repricerid = @Repricerid, @Reservationid = @Reservationid;
END TRY
BEGIN CATCH
    -- Log error but don't fail the main procedure
    PRINT 'Error in usp_upd_reservationreport_AdditionalPrebook: ' + ERROR_MESSAGE();
END CATCH
```

**Benefits**:
- **Single Point of Entry**: Application only calls one procedure
- **Atomic Operations**: Both procedures run in same transaction context
- **Error Isolation**: Additional prebook failures don't break main flow
- **Performance**: No additional network round-trips from application
- **Maintainability**: Database logic stays in database layer

---

## **🔧 SELECTION LOGIC ANALYSIS**

### **🚨 CRITICAL BUSINESS RULE**
**If a booking is already optimized (`BookingActionsTaken.ActionId = 1`), then NO additional prebook options should be shown.**

### **Business Rules Priority (Same as Original)**

1. **EXCLUDE OPTIMIZED BOOKINGS**:
   - `ISNULL(IsOptimized, 0) = 0` - Exclude already optimized
   - `ISNULL(bat_NewBookingId, 0) = 0` - Exclude BookingActionsTaken with ActionId=1

2. **Same Day Creation**: `CAST(CreateDate AS DATE) = @CurrentDate`
   - Only consider prebooks created today

3. **Cancellation Policy Status**: Complex CASE statement
   - Priority 1: `cpstatus = 'loose'` + No Giata mapping + Same day
   - Priority 2: `cpstatus = 'loose'` + Has Giata mapping + Same day
   - Priority 3: All others

4. **Profit Maximization**: `Profit DESC`
   - Higher profit wins within same priority group

5. **Recency**: `CreateDate DESC`
   - Most recent creation time wins

### **Why This Logic Works for Multiple Suppliers**

The ranking naturally selects different suppliers because:
- **Primary prebook** (rank 1) goes to `ReservationReportDetails`
- **Secondary prebooks** (ranks 2-3) go to `ReservationReportDetailsAdditionalPrebook`
- Different suppliers will have different profits/creation times
- Same business rules ensure quality and compliance

---

## **📊 DATA FLOW EXAMPLE**

### **Sample Data for Reservation ID 12345**

**ReservationTable Data**:
```
ID | ReservationId | Supplier    | Profit | CreateDate          | CPStatus | IsOptimized | ActionId | Rank
1  | 12345        | didatravel  | 25.50  | 2024-01-15 10:30:00 | loose    | 0           | NULL     | 1
2  | 12345        | sunhotels   | 23.20  | 2024-01-15 11:15:00 | loose    | 0           | NULL     | 2
3  | 12345        | booking     | 21.80  | 2024-01-15 12:00:00 | loose    | 0           | NULL     | 3
4  | 12345        | expedia     | 15.30  | 2024-01-14 09:00:00 | tight    | 0           | NULL     | 4 (excluded - different day)
5  | 67890        | didatravel  | 30.00  | 2024-01-15 09:00:00 | loose    | 1           | 1        | EXCLUDED (optimized)
```

**Result Distribution**:
- **Reservation 12345** (NOT optimized):
  - **ReservationReportDetails**: Rank 1 (didatravel, €25.50)
  - **ReservationReportDetailsAdditionalPrebook**:
    - Rank 2 (sunhotels, €23.20)
    - Rank 3 (booking, €21.80)

- **Reservation 67890** (OPTIMIZED):
  - **No additional prebooks** - excluded due to ActionId=1

---

## **🚀 DEPLOYMENT STEPS**

### **Phase 1: Database Schema**
```sql
-- 1. Create new table
CREATE TABLE ReservationReportDetailsAdditionalPrebook (...)

-- 2. Create indexes
CREATE NONCLUSTERED INDEX [IDX_RepricerID_CreateDate_AdditionalPrebook] (...)

-- 3. Create stored procedure
CREATE PROCEDURE usp_upd_reservationreport_AdditionalPrebook (...)
```

### **Phase 2: Data Population**
```sql
-- Execute for all repricers to populate historical data
EXEC usp_upd_reservationreport_AdditionalPrebook @Repricerid = 1, @Reservationid = NULL
EXEC usp_upd_reservationreport_AdditionalPrebook @Repricerid = 2, @Reservationid = NULL
-- ... for all repricers
```

### **Phase 3: Integration**
- **AUTOMATED INTEGRATION**: The `usp_upd_reservationreport` procedure now automatically calls `usp_upd_reservationreport_AdditionalPrebook`
- **NO C# CHANGES REQUIRED**: Existing application code remains unchanged
- **SINGLE CALL**: Only `usp_upd_reservationreport` needs to be called from application
- **ERROR HANDLING**: Additional prebook failures don't affect primary prebook processing

### **Phase 4: Verification**
```sql
-- Verify data population
SELECT
    r.Reservationid,
    r.Repricerid,
    r.prebooksupplier as PrimarySupplier,
    r.Profit as PrimaryProfit,
    COUNT(a.ReportId) as AdditionalPrebooksCount
FROM ReservationReportDetails r
LEFT JOIN ReservationReportDetailsAdditionalPrebook a
    ON r.Reservationid = a.Reservationid
    AND r.Repricerid = a.Repricerid
WHERE r.Repricerid = 1
GROUP BY r.Reservationid, r.Repricerid, r.prebooksupplier, r.Profit
ORDER BY r.Reservationid
```

---

## **⚠️ IMPORTANT CONSIDERATIONS**

### **Performance Impact**
- **Additional Storage**: ~2x storage for prebook data
- **Query Performance**: New indexes should maintain performance
- **Processing Time**: Additional procedure execution time

### **Data Consistency**
- **Same Day Requirement**: Ensures temporal consistency
- **Business Rule Compliance**: Maintains quality standards
- **Reference Integrity**: Links to primary prebook

### **Monitoring Points**
- **Data Volume**: Monitor growth of additional prebook table
- **Execution Time**: Track procedure performance
- **Data Quality**: Verify supplier diversity in results

---

## **🔍 TESTING QUERIES**

### **Verify Selection Logic**
```sql
-- Test the ranking logic for a specific reservation
DECLARE @Repricerid INT = 1, @ReservationId INT = 12345, @CurrentDate DATE = GETUTCDATE()

SELECT
    ROW_NUMBER() OVER (
        PARTITION BY RepricerId, ReservationID
        ORDER BY
            ISNULL(IsOptimized, 0) DESC,
            CAST(CreateDate AS DATE) DESC,
            Profit DESC,
            CreateDate DESC
    ) as Rank,
    ReservationId,
    PrebookProviders as Supplier,
    Profit,
    CreateDate,
    CPStatus
FROM ReservationTable
WHERE Repricerid = @Repricerid
  AND ReservationId = @ReservationId
  AND CAST(CreateDate AS DATE) = @CurrentDate
ORDER BY Rank
```

### **Verify Data Population**
```sql
-- Check additional prebooks for a reservation
SELECT
    a.PrebookRank,
    a.prebooksupplier,
    a.Profit,
    a.Createdate,
    p.prebooksupplier as PrimarySupplier,
    p.Profit as PrimaryProfit
FROM ReservationReportDetailsAdditionalPrebook a
JOIN ReservationReportDetails p
    ON a.PrimaryPrebookId = p.PreBookId
WHERE a.Reservationid = 12345
  AND a.Repricerid = 1
ORDER BY a.PrebookRank
```

This implementation provides a solid foundation for displaying multiple prebook options while maintaining the existing business logic and data quality standards.
