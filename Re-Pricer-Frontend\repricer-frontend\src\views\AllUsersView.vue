<template>
	<main class="container my-3">
		<div v-if="loading" class="d-flex justify-content-center">
			<Loader :class="'text-center min-vh-100 w-100'" />
		</div>
		<template v-else>
			<div class="mb-4" v-if="users.length > 0">
				<div class="card p-3 d-flex justify-content-between mb-3" v-if="isSuperAdmin">
					<div class="align-self-center fw-bold"><i class="pi pi-filter"></i> Filters</div>
					<SelectButton v-model="filters.optimizationType" :options="filters.optimizationTypes"
						optionLabel="name" />
				</div>
				<Accordion expandIcon="pi pi-plus" collapseIcon="pi pi-minus" :activeIndex="isSuperAdmin ? -1 : 0"
					:pt="{ 'headeraction': { 'class': 'p-0' } }">
					<AccordionTab v-for="(user, index) in filteredUsers" :key="index"
						:pt="{ 'headeraction': { 'class': 'p-0 pe-3' } }">
						<template #header>
							<span @click.stop
								class="align-items-md-center d-flex flex-column flex-md-row gap-2 w-full me-3 me-md-0 p-3">
								<div class="d-flex align-items-center gap-3">
									<InputSwitch @click="requireConfirmation(user.rePricerDetail)"
										v-tooltip.bottom="`${user.rePricerDetail.isJobsEnable ? 'Disable' : 'Enable'} customer CRON jobs`"
										v-if="isSuperAdmin" v-model="user.rePricerDetail.isJobsEnable"
										:invalid="!user.rePricerDetail.isJobsEnable" readonly
										:value="user.rePricerDetail.isJobsEnable" />
									<div
										class="align-items-md-baseline d-flex flex-md-column fw-normal justify-content-between">
										<div class="d-flex gap-3">
											<template v-if="isSuperAdmin">
												<span class="text-xl font-bold"> {{ user.rePricerDetail.repricerUserName
													}} </span>

											</template>
											<span class="text-xl font-bold" v-else>All Users</span>
										</div>
										<Divider class="d-none d-md-block my-2" />
										<div class="align-items-end d-flex flex-column fz-r0_9 gap-2 flex-md-row">
											<small>ID: {{ user.rePricerDetail.repricerUserID }}</small>

											<small class="d-none d-md-block">|</small>
											<small>{{ user.userInfo.length }} {{ user.userInfo.length <= 2 ? 'User'
												: 'Users' }} </small>
													<small class="d-none d-md-block">|</small>
													<small>{{ optimizationTypes[user.rePricerDetail.optimizationType]
														}}</small>
										</div>
									</div>
								</div>
								<div class="ml-auto mr-2">
									<Button label="Create user" size="small" icon="pi pi-plus"
										@click="openCreateUserDialog(user.rePricerDetail.repricerUserID)"
										:pt="userActionPT" class="ms-2 text-decoration-none" severity="primary" />
									<Button link v-if="isSuperAdmin" label="Configuration" size="small" icon="pi pi-cog"
										:pt="userActionPT"
										@click="this.$router.push({ path: `configuration/${user.rePricerDetail.repricerUserID}` })"
										class="ms-2 text-decoration-none" severity="primary" />
									<Button link v-if="isSuperAdmin" label="Report" size="small" icon="pi pi-chart-line"
										:pt="userActionPT"
										@click="this.$router.push({ path: `optimizations/optimized/${user.rePricerDetail.repricerUserID}` })"
										class="ms-2 text-decoration-none" severity="primary" />
									<Button link v-if="isSuperAdmin" label="Invoice" size="small"
										icon="pi pi-money-bill" :pt="userActionPT"
										@click="this.$router.push({ path: `invoice/${user.rePricerDetail.repricerUserID}` })"
										class="ms-2 text-decoration-none" severity="primary" />
									<Button link v-if="isSuperAdmin" label="Dashboard" size="small" icon="pi pi-desktop"
										:pt="userActionPT"
										@click="this.$router.push({ path: `dashboard/${user.rePricerDetail.repricerUserID}` })"
										class="ms-2 text-decoration-none" severity="primary" />
								</div>
							</span>
						</template>
						<DataTable class="mb-4" :loading="progress" removableSort :value="getValidUsers(user.userInfo)"
							showGridlines stripedRows :paginator="isPagination(user.userInfo)" :rows="pagesize"
							size="small" tableStyle="min-width: 50rem">
							<Column sortable field="userName" header="User Name">
								<template #body="slotProps">
									<strong>{{ slotProps.data.userName }}</strong>
									<small class="d-block text-muted">{{ slotProps.data.id }}</small>
								</template>
							</Column>
							<Column sortable field="email" header="Email ID"></Column>
							<Column sortable field="role" header="Role">

								<template #body="slotProps">
									<Chip v-for="(role, index) in slotProps.data.role" :key="index" :label="role" />
								</template>
							</Column>
							<Column header="Options">

								<template #body="slotProps">
									<Button v-tooltip.bottom="'Update Password'" icon="pi pi-shield" text
										severity="secondary" @click="updateUser(slotProps.data, 'password')" />
									<Button v-tooltip.bottom="'Edit Roles'" icon="pi pi-user-edit" text
										severity="secondary" @click="updateUser(slotProps.data, 'role')" />
									<Button v-if="slotProps.data.id != userInfo.id && slotProps.data.isLocked"
										v-tooltip.bottom="slotProps.data.isLocked ? 'Unlock' : 'Lock'"
										:icon="slotProps.data.isLocked ? 'pi pi-lock-open' : 'pi pi-lock'" text
										severity="secondary" @click="showTemplate(slotProps.data)" />
									<Button v-if="isSuperAdmin" v-tooltip.bottom="'Delete User'" icon="pi pi-trash" text
										severity="secondary" @click="showUserDeleteTemplate(slotProps.data)" />
								</template>
							</Column>
							<template #empty>
								User not available.
							</template>
						</DataTable>
					</AccordionTab>
				</Accordion>
				<Dialog v-model:visible="creatUserDialog" modal header="Create User" :style="{ width: '25rem' }">
					<div class="d-flex justify-content-between gap-1 mb-3 flex-column">
						<label for="email" class="font-semibold w-6rem">Email</label>
						<InputText id="email" class="flex-auto w-100" autocomplete="off" type="email"
							v-model="createUserForm.email" />
						<small id="email-help">The email address you provide will be used as your username.</small>
					</div>
					<div class="d-flex justify-content-between gap-1 mb-3 flex-column">
						<label for="email" class="font-semibold w-6rem">Password</label>
						<Password id="password" pt:input:class="w-100" inputClass="w-100" class="flex-auto w-100"
							autocomplete="off" v-model="createUserForm.password" />
					</div>
					<div class="d-flex justify-content-between gap-1 mb-3 flex-column">
						<label for="email" class="font-semibold flex-shrink-0">Confirm Password</label>
						<Password pt:input:class="w-100" inputClass="w-100" id="confirmPassword" class="flex-auto w-100"
							:feedback="false" :invalid="!isPasswordMatched" autocomplete="off"
							v-model="createUserForm.confirmPassword" />
					</div>
					<ul class="fz-r0_8 m-0 mb-3 ps-4">
						<li>Password must be at least 8 characters.</li>
						<li>Password must contain at least one lowercase letter.</li>
						<li>Password must contain at least one uppercase letter.</li>
						<li>Password must contain at least one special character.</li>
					</ul>
					<div class="d-flex justify-content-between gap-1 mb-3 flex-column">
						<label for="email" class="font-semibold w-6rem">Roles</label>
						<Dropdown v-model="createUserForm.roles" :options="roles" optionLabel="roleName"
							optionValue="roleName" placeholder="Select Roles" class="flex-auto w-100" />
					</div>
					<div class="d-flex justify-content-end gap-2">
						<Button type="button" label="Cancel" severity="secondary"
							@click="closeCreateUserDialog()"></Button>
						<Button type="button" :loading="createUserLoading" :disabled="!isCreateUserFormValid"
							label="Create" @click="closeAndSaveCreateUserDialog()"></Button>
					</div>
				</Dialog>
				<Dialog v-model:visible="updatePasswordDialog" modal header="Update User" :style="{ width: '25rem' }">

					<template v-if="mode == 'password'">
						<div class="d-flex justify-content-between gap-1 mb-3 flex-column">
							<label for="email" class="font-semibold w-6rem">Password</label>
							<Password id="password" pt:input:class="w-100" inputClass="w-100" class="flex-auto w-100"
								autocomplete="off" v-model="updateUserForm.password" />
						</div>
						<div class="d-flex justify-content-between gap-1 mb-3 flex-column">
							<label for="email" class="font-semibold flex-shrink-0">Confirm Password</label>
							<Password pt:input:class="w-100" inputClass="w-100" id="confirmPassword"
								class="flex-auto w-100" :feedback="false" :invalid="!isPasswordMatched"
								autocomplete="off" v-model="updateUserForm.confirmPassword" />
						</div>
					</template>

					<template v-if="mode == 'role'">
						<div class="d-flex justify-content-between gap-1 mb-3 flex-column">
							<label for="email" class="font-semibold w-6rem">Roles</label>
							<Dropdown v-model="updateUserForm.roles" :options="roles" optionLabel="roleName"
								optionValue="roleName" placeholder="Select Roles" class="flex-auto w-100" />
						</div>
					</template>
					<div class="d-flex justify-content-end gap-2">
						<Button type="button" label="Cancel" severity="secondary"
							@click="updatePasswordDialog = false"></Button>
						<Button type="button" :loading="updatePasswordLoading" :disabled="isUpdatePasswordMatched"
							label="Update" @click="closeAndUpdateUpdateUserDialog()"></Button>
					</div>
				</Dialog>
				<ConfirmDialog group="templating">

					<template #message="slotProps">
						<div class="d-flex flex-column align-items-center w-100 gap-3 border-bottom-1 surface-border">
							<i :class="slotProps.message.icon" class="fs-1 text-primary"></i>
							<p>{{ slotProps.message.message }}</p>
						</div>
					</template>
				</ConfirmDialog>
			</div>
			<div v-else class="d-flex justify-content-center">
				<Message severity="error" :closable="false">No data found.</Message>
			</div>
			<ConfirmDialog group="headless">
				<template #container="{ message, acceptCallback, rejectCallback }">
					<div class="flex flex-column align-items-center p-5 surface-overlay border-round">
						<div
							class="border-circle bg-primary inline-flex justify-content-center align-items-center h-6rem w-6rem -mt-8">
							<i class="pi pi-question text-5xl"></i>
						</div>
						<span class="font-bold text-2xl block mb-2 mt-4">{{ message.header }}</span>
						<p class="mb-0" v-html="message.message"></p>
						<div class="flex align-items-center gap-2 mt-4">
							<Button label="Save" @click="acceptCallback" class="w-8rem"></Button>
							<Button label="Cancel" outlined @click="rejectCallback" class="w-8rem"></Button>
						</div>
					</div>
				</template>
			</ConfirmDialog>
		</template>
	</main>
</template>

<script>
import Loader from './../components/Loader.vue'
import constants from '@/helpers/constants';
import { showErrorMessage } from '@/helpers/utils';
import { useAuthStore } from '@/stores/useAuthStore';
import { useRoleStore } from '@/stores/useRoleStore';
import { useUserStore } from '@/stores/useUserStore';
import Accordion from 'primevue/accordion';
import AccordionTab from 'primevue/accordiontab';
import Button from 'primevue/button';
import Chip from 'primevue/chip';
import Column from 'primevue/column';
import ConfirmDialog from 'primevue/confirmdialog';
import DataTable from 'primevue/datatable';
import Dialog from 'primevue/dialog';
import Divider from 'primevue/divider';
import InputText from 'primevue/inputtext';
import Message from 'primevue/message';
import MultiSelect from 'primevue/multiselect';
import Panel from 'primevue/panel';
import Password from 'primevue/password';
import Tag from 'primevue/tag';
import Toolbar from 'primevue/toolbar';
import Dropdown from 'primevue/dropdown';
import InputSwitch from 'primevue/inputswitch';
import { useRepricerStore } from '@/stores/useRepricerStore';
import SelectButton from 'primevue/selectbutton';

export default {
	name: "All Users",
	components: { SelectButton, DataTable, Column, Message, Button, Dialog, InputText, Password, MultiSelect, Chip, Tag, ConfirmDialog, Loader, Toolbar, Panel, Accordion, AccordionTab, Divider, Dropdown, InputSwitch },
	data() {
		return {
			filters: {
				optimizationType: '',
				optimizationTypes: [
					{ name: 'Automation', value: 3 },
					{ name: 'Semi-Automation', value: 2 }
				]
			},
			isSuperAdmin: false,
			users: [],
			adminUserId: constants.ADMIN_USER_ID,
			pagesize: 10,
			loading: true,
			progress: false,
			creatUserDialog: false,
			updatePasswordDialog: false,
			updatePasswordLoading: false,
			deleteUserLoading: false,
			createUserForm: {
				userName: "",
				email: "",
				password: "",
				confirmPassword: "",
				roles: [],
				repricerId: null
			},
			updateUserForm: null,
			roles: [],
			createUserLoading: false,
			mode: 'password',
			userActionPT: {
				label: {
					class: "d-none d-md-block"
				},
				icon: {
					class: "m-0 me-md-2"
				}
			},
			rePricerStatus: {},
			optimizationTypes: {
				"0": "Demo",
				"1": "Demo",
				"2": "Semi-Automation",
				"3": "Automation",
			}
		}
	},
	computed: {
		filteredUsers() {
			let filteredUsers = this.users;
			if (this.filters.optimizationType) {
				filteredUsers = filteredUsers.filter(user => user.rePricerDetail.optimizationType == this.filters.optimizationType.value)
			}
			return filteredUsers
		},
		userInfo() {
			const auth = useAuthStore();
			return auth.userInfo;
		},

		isUpdatePasswordMatched() {
			let isDisabled = true;
			switch (this.mode) {
				case "role":
					if (this.updateUserForm.roles && this.updateUserForm.roles.length > 0) {
						isDisabled = false;
					}
					break;
				case "password":
					if (this.updateUserForm.password && this.updateUserForm.confirmPassword && this.updateUserForm.password == this.updateUserForm.confirmPassword) {
						isDisabled = false;
					}
					break;
				default:
					break
			}

			return isDisabled
		},
		isPasswordMatched() {
			return this.createUserForm.password == this.createUserForm.confirmPassword;
		},
		isCreateUserFormValid() {
			let isValid = false;
			if (this.createUserForm.email != "" && this.createUserForm.password != "" && this.isPasswordMatched && this.createUserForm.roles.length > 0) {
				isValid = true;
			}
			return isValid
		}
	},
	methods: {
		requireConfirmation(repricerDetails) {
			const Self = this;
			let message = `You want to <strong class="text-success text-uppercase">enable</strong> ${repricerDetails.repricerUserName} CRON Jobs.`;
			if (repricerDetails.isJobsEnable) {
				message = `You want to <strong class="text-danger text-uppercase">disable</strong> ${repricerDetails.repricerUserName} CRON Jobs.`;
			}
			this.$confirm.require({
				group: 'headless',
				header: 'Are you sure?',
				message,
				accept: async () => {
					const payload = { ...repricerDetails };
					payload.isJobsEnable = !repricerDetails.isJobsEnable;
					const repricer = useRepricerStore();
					// await repricer.SetRepricerStatus({
					// 	RePricerId: repricerDetails.repricerUserID,
					// 	isActive: !repricerDetails.isActive
					// });

					// delete payload.extraClientDetail.priceDifferenceValue;
					const response = await repricer.updateRepricer(payload);
					if (response.isError) {
						this.$toast.add({ severity: 'error', summary: 'Something went wrong.', detail: response.data.errors, life: 3000 });
					} else {
						this.$toast.add({ severity: 'success', summary: 'Updated', detail: 'Status updated successfuly', life: 3000 });
					}
					await Self.getAllUsers(true);
				},
				reject: () => {
					// this.$toast.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
				}
			});
		},
		getValidUsers(users) {
			return users.filter(u => u.userName != "");
		},
		isPagination(users) {
			const validUsers = users.filter(u => u.userName != "");
			return validUsers.length > this.pagesize
		},
		updateUser(userDetails, mode) {
			this.mode = mode;
			this.updateUserForm = {
				userName: userDetails.userName,
				email: userDetails.email,
				repricerId: userDetails.repricerId
			};
			this.updatePasswordDialog = true;
			if (mode == 'password') {
			}

			if (mode == 'role') {
				this.updateUserForm['roles'] = userDetails.role[0];
			}
		},
		deleteUser(userDetails, mode) {
			this.mode = mode;
			this.updateUserForm = {
				userName: userDetails.userName,
				email: userDetails.email,
				repricerId: userDetails.repricerId
			};
			this.updatePasswordDialog = true;
			if (mode == 'password') {
			}

			if (mode == 'role') {
				this.updateUserForm['roles'] = userDetails.role;
			}
		},
		closeAndUpdateUpdateUserDialog() {
			const Self = this;
			this.updatePasswordLoading = true;
			const user = useUserStore();
			this.updateUserForm.roles = [this.updateUserForm.roles];
			user.updateUser(this.updateUserForm).then(async (response) => {
				Self.updatePasswordLoading = false;
				if (response.isError) {
					showErrorMessage(this.$toast, response, 'User update failed.')
				} else {
					Self.progress = true;
					Self.updatePasswordDialog = false;
					await this.getAllUsers(true);
				}
			})
		},
		showTemplate(userDetails) {
			const Self = this;
			let message = "Do you want to lock this user?";
			if (userDetails.isLocked) {
				message = "Do you want to unlock this user?";
			}
			this.$confirm.require({
				group: 'templating',
				header: 'Confirmation',
				message: message,
				icon: userDetails.isLocked ? 'pi pi-lock-open' : 'pi pi-lock',
				acceptIcon: 'pi pi-check',
				rejectIcon: 'pi pi-times',
				rejectClass: 'p-button-outlined p-button-sm',
				acceptClass: 'p-button-sm',
				rejectLabel: 'Cancel',
				acceptLabel: 'Save',
				accept: () => {
					const user = useUserStore();
					user.unlockUser(userDetails.userName).then(async (response) => {
						Self.updatePasswordLoading = false;
						if (response.isError) {
							showErrorMessage(this.$toast, response, 'User update failed.')
						} else {
							Self.progress = true;
							Self.updatePasswordDialog = false;
							await this.getAllUsers(true);
						}
					})
				},
				reject: () => {
					// this.$toast.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
				}
			});
		},
		showUserDeleteTemplate(userDetails) {
			const Self = this;
			let message = "Do you want to lock this user?";
			if (userDetails.isLocked) {
				message = "Do you want to unlock this user?";
			}
			this.$confirm.require({
				group: 'templating',
				header: 'Confirmation',
				message: "Do you want to delete this user?",
				icon: 'pi pi-trash',
				acceptIcon: 'pi pi-check',
				rejectIcon: 'pi pi-times',
				rejectClass: 'p-button-outlined p-button-sm',
				acceptClass: 'p-button-sm',
				rejectLabel: 'Cancel',
				acceptLabel: 'Delete',
				accept: () => {
					const user = useUserStore();
					user.deleteUser(userDetails.id).then(async (response) => {
						Self.deleteUserLoading = false;
						if (response.isError) {
							showErrorMessage(this.$toast, response, 'User delete failed.')
						} else {
							Self.progress = true;
							Self.updatePasswordDialog = false;
							await Self.getAllUsers(true);
						}
					})
				},
				reject: () => {
					// this.$toast.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
				}
			});
		},
		async getAllUsers(latest) {
			const user = useUserStore();
			user.getAllUsers(latest).then((response) => {
				this.loading = false;
				this.progress = false;
				if (!response.isError) {
					response.sort((a, b) => {
						// Check if either item has isJobsEnable set to false
						if (!a.rePricerDetail.isJobsEnable && b.rePricerDetail.isJobsEnable) return 1;
						if (a.rePricerDetail.isJobsEnable && !b.rePricerDetail.isJobsEnable) return -1;

						// If both are active or both are inactive, sort by repricerUserID
						return a.rePricerDetail.repricerUserID - b.rePricerDetail.repricerUserID;
					});
					this.users = JSON.parse(JSON.stringify(response));
				}
			})
		},
		openCreateUserDialog(repricerID) {
			this.createUserForm = {
				userName: "",
				email: "",
				password: "",
				confirmPassword: "",
				roles: [],
				repricerId: repricerID
			};
			this.creatUserDialog = true;
		},
		closeCreateUserDialog() {
			this.creatUserDialog = false;
		},
		closeAndSaveCreateUserDialog() {
			const Self = this;
			this.createUserLoading = true;
			const user = useUserStore();
			this.createUserForm.userName = this.createUserForm.email;
			this.createUserForm.roles = [this.createUserForm.roles];
			user.createUser(this.createUserForm).then(async (response) => {
				Self.createUserLoading = false;
				if (response.isError) {
					showErrorMessage(Self.$toast, response, 'Create user failed.')
				} else {
					Self.closeCreateUserDialog();
					Self.progress = true;
					await Self.getAllUsers(true);
				}
			})
		}
	},
	async mounted() {
		const auth = useAuthStore();
		const role = useRoleStore();

		this.isSuperAdmin = auth.isSuperAdmin;
		const roles = await role.getAllRoles();
		this.roles = roles.filter(r => r.roleName != "SuperAdmin");
		await this.getAllUsers(true);
	}
}

</script>
