<template>
	<main class="container my-3">
		<div v-if="loading" class="d-flex justify-content-center">
			<Loader />
		</div>
		<template v-else>
			<div class="" v-if="roles.length > 0">
				<DataTable removableSort :value="roles" showGridlines stripedRows :paginator="isPagination"
					:rows="pagesize" size="small" tableStyle="min-width: 50rem">
					<Column field="roleId" header="ID"></Column>
					<Column sortable field="roleName" header="Name"></Column>
					<Column header="Options">
						<template #body="slotProps">
							<Button v-if="slotProps.data.roleName != 'SuperAdmin'" v-tooltip.bottom="'Edit'"
								icon="pi pi-pencil" text severity="secondary" @click="updateRole(slotProps.data)" />
							<Button v-if="slotProps.data.roleName != 'SuperAdmin'" v-tooltip.bottom="'Delete'"
								icon="pi pi-trash" text severity="secondary" @click="showTemplate(slotProps.data)" />
						</template>
					</Column>

					<template #header>
						<div class="d-flex flex-wrap align-items-center justify-content-between gap-2">
							<span class="text-xl font-bold">All Roles</span>
							<Button class="p-button-sm" icon="pi pi-plus" label="Create role" severity="secondary"
								@click="openCreateRoleDialog()" />
						</div>
					</template>
				</DataTable>
				<Dialog v-model:visible="creatRoleDialog" modal header="Create Role" :style="{ width: '25rem' }">
					<div class="d-flex justify-content-between gap-1 mb-3 flex-column">
						<label for="rolename" class="font-semibold w-6rem small">Role Name</label>
						<InputText id="rolename" class="flex-auto w-100" autocomplete="off"
							v-model="createRoleForm.roleName" />
					</div>
					<div class="d-flex justify-content-end gap-2">
						<Button type="button" label="Cancel" severity="secondary"
							@click="closeCreateRoleDialog()"></Button>
						<Button type="button" :loading="createRoleLoading" :disabled="!isCreateRoleFormValid"
							label="Create" @click="closeAndSaveCreateRoleDialog()"></Button>
					</div>
				</Dialog>
				<Dialog v-model:visible="updateRoleDialog" modal header="Update Role" :style="{ width: '25rem' }">

					<div class="d-flex justify-content-between gap-1 mb-3 flex-column">
						<label for="email" class="font-semibold w-6rem">Role Name</label>
						<InputText id="roleName" class="flex-auto w-100" autocomplete="off"
							v-model="updateRoleForm.roleName" />
					</div>
					<div class="d-flex justify-content-end gap-2">
						<Button type="button" label="Cancel" severity="secondary"
							@click="updateRoleDialog = false"></Button>
						<Button type="button" :loading="updateRoleLoading" :disabled="updateRoleForm.roleName == ''"
							label="Update" @click="closeAndUpdateUpdateRoleDialog()"></Button>
					</div>
				</Dialog>
				<ConfirmDialog group="templating">

					<template #message="slotProps">
						<div class="d-flex flex-column align-items-center w-100 gap-3 border-bottom-1 surface-border">
							<i :class="slotProps.message.icon" class="fs-1 text-primary"></i>
							<p>{{ slotProps.message.message }}</p>
						</div>
					</template>
				</ConfirmDialog>
			</div>
			<div v-else class="d-flex justify-content-center">
				<Message severity="error" :closable="false">No data found.</Message>
			</div>
		</template>
	</main>
</template>

<script>
import Loader from './../components/Loader.vue'
import constants from '@/helpers/constants';
import { showErrorMessage } from '@/helpers/utils';
import Button from 'primevue/button';
import Column from 'primevue/column';
import ConfirmDialog from 'primevue/confirmdialog';
import DataTable from 'primevue/datatable';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import Message from 'primevue/message';
import { useRoleStore } from '@/stores/useRoleStore';

export default {
	name: "All Roles",
	components: { DataTable, Column, Message, Button, Dialog, InputText, ConfirmDialog, Loader },
	data() {
		return {
			pagesize: 10,
			loading: true,
			creatRoleDialog: false,
			updateRoleDialog: false,
			updateRoleLoading: false,
			createRoleForm: {
				roleName: ""
			},
			updateRoleForm: null,
			roles: [],
			createRoleLoading: false,
		}
	},
	computed: {
		isPagination() {
			return this.roles.length > this.pagesize
		},
		isCreateRoleFormValid() {
			return this.createRoleForm.roleName != ""
		}
	},
	methods: {
		updateRole(roleDetails, mode) {
			this.mode = mode;
			this.updateRoleForm = {
				roleName: roleDetails.roleName,
				id: roleDetails.roleId,
			};
			this.updateRoleDialog = true;
		},
		closeAndUpdateUpdateRoleDialog() {
			const Self = this;
			this.updateRoleLoading = true;
			const role = useRoleStore();
			role.updateRole(this.updateRoleForm).then(async (response) => {
				Self.updateRoleLoading = false;
				if (response.isError) {
					showErrorMessage(this.$toast, response, 'Role update failed.')
				} else {
					Self.loading = true;
					Self.updateRoleDialog = false;
					this.roles = await role.getAllRoles();
					this.loading = false;
				}
			})
		},
		showTemplate(roleDetails) {
			const Self = this;
			let message = "Do you want to remove this role?";
			this.$confirm.require({
				group: 'templating',
				header: 'Confirmation',
				message: message,
				icon: 'pi pi-trash',
				acceptIcon: 'pi pi-check',
				rejectIcon: 'pi pi-times',
				rejectClass: 'p-button-outlined p-button-sm',
				acceptClass: 'p-button-sm',
				rejectLabel: 'Cancel',
				acceptLabel: 'Delete',
				accept: () => {
					const role = useRoleStore();
					role.deleteRole(roleDetails.roleId).then(async (response) => {
						Self.updateRoleLoading = false;
						if (response.isError) {
							showErrorMessage(this.$toast, response, 'Role delete failed.')
						} else {
							Self.loading = true;
							Self.updateRoleDialog = false;
							this.roles = await role.getAllRoles();
							this.loading = false;
						}
					})
				},
				reject: () => {
					// this.$toast.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected', life: 3000 });
				}
			});
		},
		openCreateRoleDialog() {
			this.createRoleForm = {
				roleName: ""
			};
			this.creatRoleDialog = true;
		},
		closeCreateRoleDialog() {
			this.creatRoleDialog = false;
		},
		closeAndSaveCreateRoleDialog() {
			this.createRoleLoading = true;
			const role = useRoleStore();
			role.createRole(this.createRoleForm).then(async (response) => {
				this.createRoleLoading = false;
				if (response.isError) {
					showErrorMessage(this.$toast, response, 'Create role failed.')
				} else {
					this.loading = true;
					this.closeCreateRoleDialog();
					this.roles = await role.getAllRoles();
					this.loading = false;
				}
			})
		}
	},
	async mounted() {
		const role = useRoleStore();
		this.roles = await role.getAllRoles();
		this.loading = false;
	}
}

</script>
