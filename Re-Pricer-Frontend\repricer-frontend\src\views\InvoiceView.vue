<script>
import moment from 'moment';
import Loader from './../components/Loader.vue';
import { useRepricerStore } from '@/stores/useRepricerStore';

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Calendar from 'primevue/calendar';
import Card from 'primevue/card';
import Button from 'primevue/button';
import { useUserStore } from '@/stores/useUserStore';
import { downloadJSONAsExcel } from '@/helpers/utils';
import { useAuthStore } from '@/stores/useAuthStore';
import Message from 'primevue/message';


export default {
    name: "Configuration",
    components: {
        Loader, DataTable, Column, Calendar, Card, Button, Message
    },
    data() {
        return {
            isDeveloper: false,
            loading: true,
            invoiceInfo: null,
            reportRange: moment().subtract(1, 'months').startOf('month').format('MMMM yy'),
            bookingInfoCard: {
                body: {
                    class: 'p-0'
                },
                footer: {
                    class: 'fz-r0_9 text-muted max-column-text'
                },
                title: {
                    class: 'max-column-text'
                }
            },
            auth: null,
            lastDateOfprevMonth: null,
            expandedRows: {},
            completeRecords: [],
            products: [
                {
                    id: '1000',
                    code: 'f230fh0g3',
                    name: 'Bamboo Watch',
                    description: 'Product Description',
                    image: 'bamboo-watch.jpg',
                    price: 65,
                    category: 'Accessories',
                    quantity: 24,
                    inventoryStatus: 'INSTOCK',
                    rating: 5,
                    orders: [
                        {
                            id: '1000-0',
                            productCode: 'f230fh0g3',
                            date: '2020-09-13',
                            amount: 65,
                            quantity: 1,
                            customer: 'David James',
                            status: 'PENDING'
                        }
                    ]
                }
            ],
            dataRelaoding: false,
            isSuperAdmin: false
        }
    },
    computed: {
        totalProfit() {
            let profit = '';
            if (this.completeRecords[0]) {
                let amount = 0;
                this.completeRecords.map(r => {
                    amount = amount + r.optimizationProfit;
                    return r
                })
                profit = this.$filters.priceDisplay(amount, this.completeRecords[0].currency);
            }
            return profit
        },
        totalInvoiceable() {
            let profit = '';
            if (this.completeRecords[0]) {
                let amount = 0;
                this.completeRecords.map(r => {
                    amount = amount + r.optimizationProfit;
                    return r
                })
                profit = this.$filters.priceDisplay(amount / 2, this.completeRecords[0].currency);
            }
            return profit
        },
        isNextMonthSelected() {
            let isFromFutureMonth = false;
            if (this.reportRange) {
                const givenDate = moment(new Date(this.reportRange));
                isFromFutureMonth = givenDate.isAfter(this.lastDateOfprevMonth, 'month');
            }
            return isFromFutureMonth
        }
    },
    methods: {
        async getInvoiceReport(isFesh) {
            this.loading = true;
            const auth = useAuthStore();
            this.isDeveloper = auth.userInfo.roleName.indexOf("Developer") >= 0;
            this.expandedRows = {};
            const user = useUserStore();
            user.users.length == 0 && await user.getAllUsers()

            this.lastDateOfprevMonth = new Date(moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD'));
            const repricerInfo = useRepricerStore();
            let repricerId = repricerInfo.repricer.repricerUserID;

            if (this.$route.params.repricerId) {
                repricerId = this.$route.params.repricerId;
            }

            if (new Date(this.reportRange).toString().toLowerCase() == "invalid date") {
                this.reportRange = "01 " + this.reportRange
            }
            const givenDate = moment(new Date(this.reportRange));

            let params = {
                "repricerId": repricerId,
                "fromDate": moment(givenDate).startOf('month').format('YYYY-MM-DD'),
            }
            if (givenDate.isSame(moment(), 'month')) {
                params["toDate"] = moment().format('YYYY-MM-DD')
            } else {
                params["toDate"] = moment(givenDate).endOf('month').format('YYYY-MM-DD')
            }

            if (isFesh) {
                params["isCached"] = false
            }
            const { reservationReports } = await repricerInfo.GetInvoiceReport(params);
            this.completeRecords = reservationReports;
            const arrInvoiceInfo = user.users.map(u => {
                u["id"] = u.rePricerDetail.repricerUserID;
                const currentUserInvoie = reservationReports.filter(r => r.repricerId == u.rePricerDetail.repricerUserID);
                if (currentUserInvoie.length > 0) {
                    u["totalProfit"] = currentUserInvoie.reduce((acc, r) => acc + r.optimizationProfit, 0).toFixed(2);
                    u["totalInvoiceable"] = currentUserInvoie.reduce((acc, r) => acc + r.optimizationProfit / 2, 0).toFixed(2);
                    u["currency"] = currentUserInvoie[0].currency;
                }

                u["invoice"] = currentUserInvoie;
                return u
            });
            this.invoiceInfo = arrInvoiceInfo.filter(u => u.invoice.length > 0).sort((a, b) => b.totalProfit - a.totalProfit);

            if (this.invoiceInfo.length == 1) {
                this.expandedRows[this.invoiceInfo[0].id] = true
            }

            this.loading = false;
            this.dataRelaoding = false;
        },
        exportCSV(elm, name) {
            let jsonData = this.completeRecords;
            let fileName = 'Invoice_Report';
            let requiredProperties = ['reservationId', 'newReservationId', 'reservation.checkIn', 'reservation.price', 'optimizationProfit', 'invoiceable', 'currency'];

            if (name) {
                const currentCustomer = this.invoiceInfo.find(i => i.rePricerDetail.repricerUserID == name);
                jsonData = currentCustomer.invoice;
                fileName = currentCustomer.rePricerDetail.repricerUserName + '_Invoice_Report';
            } else {
                requiredProperties = ['repricerId', 'repricerName', ...requiredProperties];
            }
			downloadJSONAsExcel(jsonData, fileName, requiredProperties, this.invoiceInfo);
        },


        onRowExpand(event) {
            // this.$toast.add({ severity: 'info', summary: 'Product Expanded', detail: event.data.name, life: 3000 });
        },
        onRowCollapse(event) {
            // this.$toast.add({ severity: 'success', summary: 'Product Collapsed', detail: event.data.name, life: 3000 });
        },
        expandAll() {
            this.expandedRows = this.invoiceInfo.reduce((acc, p) => (acc[p.id] = true) && acc, {});
        },
        collapseAll() {
            this.expandedRows = null;
        },
        formatCurrency(value) {
            return value.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
        },
        getSeverity(product) {
            switch (product.inventoryStatus) {
                case 'INSTOCK':
                    return 'success';

                case 'LOWSTOCK':
                    return 'warn';

                case 'OUTOFSTOCK':
                    return 'danger';

                default:
                    return null;
            }
        },
        getOrderSeverity(order) {
            switch (order.status) {
                case 'DELIVERED':
                    return 'success';

                case 'CANCELLED':
                    return 'danger';

                case 'PENDING':
                    return 'warn';

                case 'RETURNED':
                    return 'info';

                default:
                    return null;
            }
        }
    },
    async mounted() {
        // this.getInvoiceReport();
        // this.loading = false

        const auth = useAuthStore();
        this.isSuperAdmin = auth.isSuperAdmin;
    },
    watch: {
        reportRange: function (newValue) {
            this.getInvoiceReport();
        },
        '$route.params.repricerId': {
            immediate: true,
            handler(newId, oldId) {
                this.getInvoiceReport();
            }
        }
    },
}

</script>
<template>
    <main class="container my-3 min-vh-100">
        <Loader v-if="loading" class="text-center" />
        <template v-else>
            <DataTable v-model:expandedRows="expandedRows" :value="invoiceInfo" dataKey="id" @rowExpand="onRowExpand" :loading="dataRelaoding"
                @rowCollapse="onRowCollapse" tableStyle="min-width: 60rem">
                <template #header>
                    <div class="flex justify-content-between">
                        <div class="align-self-center fw-bolder">Invoice</div>
                        <div class="flex flex-wrap justify-end gap-3">
                            <template v-if="invoiceInfo.length > 1">
                                <Button v-if="isDeveloper" icon="pi pi-refresh" @click="getInvoiceReport(true)"></Button>
                                <Button icon="pi pi-download" label="Download invoice for all customers" @click="exportCSV" severity="secondary" aria-label="Submit" />
                                <div class="d-none">
                                    <Button icon="pi pi-plus" severity="primary" @click="expandAll" size="small" />
                                    <Button icon="pi pi-minus" @click="collapseAll" size="small" />
                                </div>
                            </template>
                            <Calendar v-model="reportRange" :maxDate="isSuperAdmin ? null : lastDateOfprevMonth" view="month" dateFormat="MM yy" :manualInput="false" showIcon />
                        </div>
                    </div>
                </template>
                <Column expander style="width: 5rem" v-if="invoiceInfo.length > 1" />
                <Column field="name" header="Customer Name">
                    <template #body="slotProps">
                        {{ slotProps.data.rePricerDetail.repricerUserName }}
                    </template>
                </Column>
                <Column field="totalProfit" header="Reservation Count">
                    <template #body="slotProps">
                        {{ slotProps.data.invoice.length }}
                    </template>
                </Column>
                <Column field="totalProfit" header="Total Realized Gain">
                    <template #body="slotProps">
                        <span class="text-primary">
                            {{ slotProps.data.totalProfit }} {{ slotProps.data.currency }}
                        </span>
                    </template>
                </Column>
                <Column field="totalInvoiceable" header="Total Invoiceable">
                    <template #body="slotProps">
                        <span class="text-primary">
                            {{ slotProps.data.totalInvoiceable }} {{ slotProps.data.currency }}
                        </span>
                    </template>
                </Column>
                <Column header="Download Invoice">
                    <template #body="slotProps">
                        <Button icon="pi pi-download" @click="exportCSV($event, slotProps.data.rePricerDetail.repricerUserID)" severity="secondary" aria-label="Submit" />
                    </template>
                </Column>
                <template #expansion="slotProps">
                    <div>
                        <template v-if="totalProfit && invoiceInfo.length == 1 && isSuperAdmin">
                            <Message v-if="isNextMonthSelected" severity="warn" :closable="false">
                                This report is based on preliminary data. Final numbers may differ at the end of the selected month.
                            </Message>
                        </template>
                        <DataTable :value="slotProps.data.invoice" tableStyle="min-width: 50rem" :ref="slotProps.data.rePricerDetail.repricerUserName" 
                        :paginator="slotProps.data.invoice.length > 5" :rows="5" :rowsPerPageOptions="[5, 10, 20, 50]"
                            exportFilename="invoice">
                            <template #empty>
                                <div class="my-5">No data found.</div>
                            </template>
                            <Column field="reservationId" header="Original Reservation" :exportable="true">
                                <template #body="slotProps">
                                    <Card :pt="bookingInfoCard" class="bg-transparent shadow-none">
                                        <template #content>
                                            <div class="d-flex fz-r0_9 justify-content-between flex-column">
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Reservation ID</span>
                                                    <span class="max-column-text text-end">
                                                        {{ slotProps.data.reservationId }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between"
                                                    v-if="slotProps.data.reservation">
                                                    <span class="text-muted fz-r0_8">Check-in</span>
                                                    <span class="max-column-text text-end">
                                                        {{ $filters.date(slotProps.data.reservation.checkIn) }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between"
                                                    v-if="slotProps.data.reservation.cancellationPolicyStartDate">
                                                    <span class="text-muted fz-r0_8">Canc. policy start</span>
                                                    <span class="max-column-text text-end">
                                                        {{
                                                            $filters.dateAndTime(slotProps.data.reservation.cancellationPolicyStartDate)
                                                        }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Reservation price</span>
                                                    <span class="max-column-text text-end">
                                                        {{ $filters.priceDisplay(slotProps.data.reservation.price,
                                                            slotProps.data.currency) }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Status</span>
                                                    <span class="max-column-text text-end">
                                                        {{ slotProps.data.reservation.status }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Cancellation policy type</span>
                                                    <span class="max-column-text text-end">
                                                        {{ slotProps.data.reservation.cancellationPolicyType }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Last activity</span>
                                                    <span class="max-column-text text-end"
                                                        v-if="slotProps.data.reservation.lastActivity">
                                                        {{ $filters.dateAndTime(slotProps.data.reservation.lastActivity)
                                                        }}
                                                    </span>
                                                    <span v-else>--</span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Cancelled on date</span>
                                                    <span class="max-column-text text-end"
                                                        v-if="slotProps.data.reservation.cancelledOnDate">
                                                        {{ slotProps.data.reservation.cancelledOnDate }}
                                                    </span>
                                                    <span v-else>--</span>
                                                </div>
                                            </div>
                                        </template>
                                    </Card>
                                </template>
                            </Column>
                            <Column field="newReservationId" header="Optimized Reservation" :exportable="true">
                                <template #body="slotProps">
                                    <Card :pt="bookingInfoCard" class="bg-transparent shadow-none">
                                        <template #content>
                                            <div class="d-flex fz-r0_9 justify-content-between flex-column">
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Reservation ID</span>
                                                    <span class="max-column-text text-end">
                                                        {{ slotProps.data.newReservationId }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between"
                                                    v-if="slotProps.data.prebook">
                                                    <span class="text-muted fz-r0_8">Check-in</span>
                                                    <span class="max-column-text text-end">
                                                        {{ $filters.date(slotProps.data.prebook.checkIn) }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between"
                                                    v-if="slotProps.data.prebook.cancellationPolicyStartDate">
                                                    <span class="text-muted fz-r0_8">Canc. policy start</span>
                                                    <span class="max-column-text text-end">
                                                        {{
                                                            $filters.dateAndTime(slotProps.data.prebook.cancellationPolicyStartDate)
                                                        }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Reservation price</span>
                                                    <span class="max-column-text text-end">
                                                        {{ $filters.priceDisplay(slotProps.data.prebook.price,
                                                            slotProps.data.currency)
                                                        }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Status</span>
                                                    <span class="max-column-text text-end">
                                                        {{ slotProps.data.prebook.status }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Cancellation policy type</span>
                                                    <span class="max-column-text text-end">
                                                        {{ slotProps.data.prebook.cancellationPolicyType }}
                                                    </span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Last activity</span>
                                                    <span class="max-column-text text-end"
                                                        v-if="slotProps.data.prebook.lastActivity">
                                                        {{ $filters.dateAndTime(slotProps.data.prebook.lastActivity) }}
                                                    </span>
                                                    <span v-else>--</span>
                                                </div>
                                                <div class="d-flex gap-3 justify-content-between">
                                                    <span class="text-muted fz-r0_8">Cancelled on date</span>
                                                    <span class="max-column-text text-end"
                                                        v-if="slotProps.data.prebook.cancelledOnDate">
                                                        {{ slotProps.data.prebook.cancelledOnDate }}
                                                    </span>
                                                    <span v-else>--</span>
                                                </div>
                                            </div>
                                        </template>
                                    </Card>
                                </template>
                            </Column>
                            <Column field="optimizationProfit" header="Realized Gain" :exportable="false">
                                <template #body="slotProps">
                                    {{ $filters.priceDisplay(slotProps.data.optimizationProfit, slotProps.data.currency)
                                    }}
                                </template>
                            </Column>
                            <Column field="optimizationProfit" header="Invoiceable" :exportable="false">
                                <template #body="slotProps">
                                    {{ $filters.priceDisplay(slotProps.data.optimizationProfit / 2,
                                    slotProps.data.currency) }}
                                </template>
                            </Column>
                            <Column field="reservation.checkIn" header="Check-in" :exportable="true" hidden></Column>
                            <Column field="reservation.cancellationPolicyStartDate" header="Original Canc. policy start"
                                :exportable="true" hidden>
                            </Column>
                            <Column field="prebook.cancellationPolicyStartDate" header="Optimized Canc. policy start"
                                :exportable="true" hidden>
                            </Column>
                            <Column field="reservation.price" header="Original price" :exportable="true" hidden>
                            </Column>
                            <Column field="prebook.price" header="Optimized price" :exportable="true" hidden></Column>
                            <Column field="optimizationProfit" header="Realized Gain" :exportable="true" hidden>
                            </Column>
                            <Column field="currency" header="Currency" :exportable="true" hidden></Column>
                        </DataTable>
                    </div>
                </template>
                <template #footer v-if="totalProfit && invoiceInfo.length > 1">
                    <div class="flex justify-content-between">
                        <div class="align-self-center">
                            <Message v-if="isNextMonthSelected && isSuperAdmin" severity="warn" :closable="false">
                                This report is based on preliminary data. Final numbers may differ at the end of the selected month.
                            </Message>
                        </div>
                        <div>
                            <div class="text-end">Total Reservation Count: {{ completeRecords.length }}</div>
                            <div class="text-end">Total Realized Gain: {{ totalProfit }}</div>
                            <div class="text-end">Total Invoiceable: {{ totalInvoiceable }}</div>
                        </div>
                    </div>
                </template>
            </DataTable>
        </template>
    </main>
</template>
