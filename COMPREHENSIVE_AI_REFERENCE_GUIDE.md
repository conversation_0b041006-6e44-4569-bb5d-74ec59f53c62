# COMPREHENSIVE AI REFERENCE GUIDE
## Hotel Re-Optimization Platform - Complete Technical Context & Coding Standards

---

## 🚨 CRITICAL: READ THIS FIRST - TODAY'S MISTAKES & LESSONS

### **❌ SPECIFIC MISTAKES MADE TODAY (2025-01-27)**

#### **1. REQUIREMENT MISUNDERSTANDING**
- **Mistake**: Overcomplicated simple requirement "ONE ROW PER SUPPLIER with MAX PROFIT"
- **Impact**: Wasted hours on complex logic when simple solution was needed
- **Root Cause**: Didn't read requirement carefully enough

#### **2. CODE ANALYSIS FAILURES**
- **Mistake**: Made assumptions about column names without verification
- **Example**: Used `PrebookProviders` when actual column was `PrebookSupplier`
- **Impact**: SQL errors and broken procedures

#### **3. WINDOWING FUNCTION ERRORS**
- **Mistake**: Used `ROW_NUMBER()` in WHERE clause (not allowed)
- **Impact**: Syntax errors that prevented procedure execution
- **Root Cause**: Didn't verify SQL syntax before suggesting

#### **4. MISSED ROOT CAUSE**
- **Mistake**: Focused on PARTITION BY logic when real issue was `firstcreatedate` timestamps
- **Impact**: Solved wrong problem, duplicates remained
- **Root Cause**: Didn't analyze actual data properly

#### **5. INTRODUCED NEW ERRORS**
- **Mistake**: Column mismatch between explicit SELECT and `t.*` selection
- **Impact**: Would have caused runtime errors
- **Root Cause**: Didn't reverify changes line-by-line

### **🎯 MANDATORY PROTOCOLS TO PREVENT FUTURE MISTAKES**

#### **PROTOCOL 1: TRIPLE-READ REQUIREMENT**
```
1. Read user requirement 3 times minimum
2. Restate requirement back to user for confirmation
3. Ask specific clarifying questions if ANY uncertainty
4. Never assume what user "probably" means
```

#### **PROTOCOL 2: MANDATORY CODE EXAMINATION**
```
1. Read ALL relevant code line-by-line before making changes
2. Verify EVERY column name against actual table schemas
3. Check EVERY stored procedure name in Constants files
4. Trace complete data flow from source to target
5. Never assume column names, case sensitivity, or structures
```

#### **PROTOCOL 3: INCREMENTAL CHANGES**
```
1. Make ONE small change at a time
2. Verify each change before proceeding
3. Ask for user feedback after each step
4. Never make multiple complex changes simultaneously
```

#### **PROTOCOL 4: MANDATORY REVERIFICATION**
```
1. Re-read modified code line-by-line before submitting
2. Check for syntax errors, logic errors, column mismatches
3. Verify the change actually addresses the specific requirement
4. Ensure no new errors are introduced
```

#### **PROTOCOL 5: HONEST UNCERTAINTY**
```
1. If not 100% certain, say "I'm not sure" instead of guessing
2. Ask user to verify critical assumptions
3. Admit when I don't understand something
4. Request clarification rather than making assumptions
```

---

## 🏗️ SYSTEM ARCHITECTURE OVERVIEW

### **Business Model**
- **Platform**: Automated hotel booking arbitrage that finds better deals for existing reservations
- **Revenue**: 50% commission on savings achieved (client retains 50%)
- **Risk Model**: Zero-risk billing - only charge after guest check-in and risk elimination

### **Core Technology Stack**
- **Database**: SQL Server (primary) + MongoDB (Giata room mapping)
- **Backend**: .NET Core Web API with dependency injection
- **Background**: HangFire jobs for automated optimization
- **Caching**: Multi-level (Memory 5min → Redis 6hr → Database)
- **APIs**: IRIX hotel booking API, Giata room mapping service

### **Two Distinct Optimization Flows**

#### **Flow 1: Same Supplier Rebooking**
- **File**: `Irix.Service/SearchService.cs`
- **Method**: `_1_PrebookAndOptimize_SameSupplier_Automatic`
- **Strategy**: Find better rates with same supplier
- **Characteristics**: Lower risk, can execute automatically, faster processing

#### **Flow 2: Multi-Supplier Rebooking**
- **File**: `Irix.Service/SupplierSearchService.cs`
- **Method**: `_1_PrebookAndOptimize_MultiSupplier`
- **Strategy**: Find better rates across different suppliers using Giata mapping
- **Characteristics**: Maximum savings potential, requires manual approval, complex validation

---

## 📊 CRITICAL DATABASE ARCHITECTURE

### **3-Layer Data Processing Pipeline**
```
Layer 1: usp_upd_reservationreport (Data consolidation + currency conversion)
Layer 2: usp_ins_upd_tbl_vw_ResevationReports (Business logic + report classification)
Layer 3: usp_get_ResevationReports_V1 (API access + caching)
```

### **Core Tables & Their Roles**

#### **ReservationMain** - Source of Truth
- **Purpose**: Primary reservation data from client systems
- **Key Fields**: ReservationId, RepricerId, CheckIn, CheckOut, supplierName

#### **ReservationTable** - Qualified Candidates
- **Purpose**: Contains SELECTED CANDIDATES FOR REBOOKING after validation
- **Critical**: Only populated AFTER successful IRIX prebook + validation
- **Key Fields**: PreBookPrice, Profit, CPStatus, AvailabilityToken

#### **BookingActionsTaken** - Final Results
- **Purpose**: Record of all optimization attempts and results
- **Critical**: Source of truth for completed optimizations and invoicing
- **Key Fields**: ActionId, NewBookingId, ProfitAmount, OptimizationMethod

#### **ReservationReportDetails** - Consolidated Data
- **Purpose**: Layer 1 - Data consolidation with currency conversion
- **Role**: Bridge between raw data and business intelligence

#### **tbl_vw_ResevationReports** - Business Intelligence
- **Purpose**: Layer 2 - Business logic application and report classification
- **Role**: Primary data source for GetRepricerReport API

### **Critical Gateway Procedures**

#### **usp_Ins_Prebook_V1** - THE MAIN GATEWAY
- **Purpose**: Populates ReservationTable with qualified candidates
- **Critical**: Only called after successful IRIX prebook + ALL validations passed
- **Without This**: No reservations can proceed to optimization

#### **usp_Ins_Prebooklog_V1** - LOGGING GATEWAY
- **Purpose**: Populates ReservationTablelog (historical data)
- **Note**: Different from main gateway - this is for logging only

---

## 🔧 CODING STANDARDS & REQUIREMENTS

### **Database Changes Protocol**
1. **Read line-by-line** before making changes
2. **Verify every column name** against actual schemas
3. **Never assume case sensitivity** or column existence
4. **Use incremental deployment** with zero downtime
5. **Deploy application code first**, then database updates

### **Package Management Rules**
- **ALWAYS use package managers** (npm, pip, cargo, etc.)
- **NEVER manually edit** package.json, requirements.txt, etc.
- **Package managers handle** versions, conflicts, dependencies correctly

### **Error Handling Standards**
- **Add console logs** only for exceptions
- **Wrap code in try-catch** blocks where necessary
- **Provide meaningful error messages**
- **Include rollback scripts** for safety

### **Testing Requirements**
- **Never use repricerId = 12** (production ID)
- **Use repricerId = 99** for testing
- **Test both optimization flows** (Same Supplier + Multi-Supplier)
- **Verify complete workflow**, not isolated components

---

## 🚫 CRITICAL MISTAKES TO AVOID

### **Column Name Assumptions**
- ❌ NEVER assume column names without checking actual table schemas
- ❌ NEVER use `PrebookProviders` when actual column is `PrebookSupplier`
- ❌ NEVER mix case (`PrebookChildAges` vs `Prebookchildages`) without verification
- ✅ ALWAYS verify column names in actual table definitions

### **Stored Procedure Assumptions**
- ❌ NEVER assume procedure names without checking Constants files
- ❌ NEVER assume `usp_Ins_Prebooklog_V1` populates `ReservationTable` (it populates `ReservationTablelog`)
- ✅ ALWAYS read the actual stored procedure to see what table it operates on

### **Code Reference Assumptions**
- ❌ NEVER copy-paste code without adapting ALL column references
- ❌ NEVER assume similar procedures have identical column names
- ✅ ALWAYS validate each column reference against the target context

### **SQL Syntax Errors**
- ❌ NEVER use windowing functions in WHERE clauses
- ❌ NEVER assume CTE column availability without explicit selection
- ✅ ALWAYS verify SQL syntax before suggesting changes

---

## 📋 MANDATORY PRE-CODE CHECKLIST

### **Before Making ANY Code Changes:**
- [ ] Read user requirement 3 times minimum
- [ ] Examine actual data/code line-by-line
- [ ] Verify column names, structures, data types
- [ ] Understand complete data flow
- [ ] Ask clarifying questions if ANY uncertainty exists
- [ ] Make only minimal, focused changes
- [ ] Reverify changes line-by-line before submitting

### **Verification Checklist:**
- [ ] Read complete target method/procedure line by line
- [ ] Verify all column names against actual table schemas
- [ ] Check all stored procedure names in Constants files
- [ ] Cross-reference all column mappings between source and target
- [ ] Validate case sensitivity for all database object names
- [ ] Test all assumptions against actual code/schema definitions

---

## 🎯 SUCCESS CRITERIA

### **Successful Code Assistance:**
- ✅ Requirement understood correctly
- ✅ Minimal, focused changes made
- ✅ No new errors introduced
- ✅ Existing functionality preserved
- ✅ User's exact need addressed
- ✅ Code follows existing patterns

### **Failed Code Assistance:**
- ❌ Misunderstood requirements
- ❌ Introduced bugs or errors
- ❌ Overcomplicated solution
- ❌ Broke existing functionality
- ❌ Made assumptions without verification
- ❌ Wasted user's time

---

## 🚨 WHEN TO ASK FOR HELP

### **Ask User When:**
- Requirements are unclear or ambiguous
- Multiple approaches are possible
- Uncertain about data structures
- Need to break existing functionality
- Unsure about business rules
- Code analysis reveals complexity

### **Never Guess:**
- Column names or data types
- Business logic requirements
- Database relationships
- User preferences
- Performance implications

---

## 📝 COMMITMENT

I commit to following these protocols rigorously to provide reliable, high-quality code assistance that respects the user's time and requirements.

**If I cannot meet these standards, I will ask for clarification rather than provide potentially incorrect solutions.**

---

## 📚 QUICK REFERENCE

### **Key Business Rules**
- **Additional Prebook**: ONE ROW PER SUPPLIER with MAX PROFIT and BEST CANCELLATION POLICY
- **Commission**: 50% of savings to platform, 50% to client
- **Risk-Free Billing**: Only charge after guest check-in and cancellation deadlines passed
- **Optimization Types**: Same Supplier (automatic) vs Multi-Supplier (manual approval)

### **Critical Files**
- **Same Supplier**: `Irix.Service/SearchService.cs`
- **Multi-Supplier**: `Irix.Service/SupplierSearchService.cs`
- **Database Projects**: Located in `Database/` folder, named exactly as databases
- **Constants**: Check for stored procedure names, never assume

### **Testing Guidelines**
- **Test RepricerId**: Use 99 (never 12 - production)
- **Test Both Flows**: Same Supplier + Multi-Supplier
- **Verify Complete Pipeline**: Source → Processing → Reporting
- **Check Data Consistency**: Across all tables and layers

This comprehensive guide ensures I understand the complete system context and follow rigorous protocols to avoid the mistakes made today.
