-- =============================================
-- FIX DATA TYPE ISSUES IN ReservationReportDetailsAdditionalPrebook
-- Author: Augment Agent
-- Date: 2025-01-27
-- Description: Fix data type mismatches causing conversion errors
-- =============================================

USE [rpndb]
GO

PRINT '=========================================='
PRINT 'FIXING DATA TYPE ISSUES'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

-- Check if table exists
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ReservationReportDetailsAdditionalPrebook]') AND type in (N'U'))
BEGIN
    PRINT 'Table ReservationReportDetailsAdditionalPrebook exists - checking data types...'
    
    -- Check current data types for the problematic columns
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        CHARACTER_MAXIMUM_LENGTH,
        NUMERIC_PRECISION,
        NUMERIC_SCALE
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ReservationReportDetailsAdditionalPrebook'
      AND COLUMN_NAME IN ('MatchedReservationCancellationChargeByPolicy', 'MatchedPreBookCancellationChargeByPolicy')
    ORDER BY COLUMN_NAME;
    
    -- Check if columns need to be altered
    DECLARE @NeedsAlter BIT = 0;
    
    IF EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'ReservationReportDetailsAdditionalPrebook'
          AND COLUMN_NAME = 'MatchedReservationCancellationChargeByPolicy'
          AND DATA_TYPE = 'decimal'
    )
    BEGIN
        SET @NeedsAlter = 1;
        PRINT 'MatchedReservationCancellationChargeByPolicy is DECIMAL - needs to be changed to VARCHAR(MAX)'
    END
    
    IF EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'ReservationReportDetailsAdditionalPrebook'
          AND COLUMN_NAME = 'MatchedPreBookCancellationChargeByPolicy'
          AND DATA_TYPE = 'decimal'
    )
    BEGIN
        SET @NeedsAlter = 1;
        PRINT 'MatchedPreBookCancellationChargeByPolicy is DECIMAL - needs to be changed to VARCHAR(MAX)'
    END
    
    IF @NeedsAlter = 1
    BEGIN
        PRINT ''
        PRINT 'Altering table structure...'
        
        -- Clear any existing data first (since we're changing data types)
        DELETE FROM ReservationReportDetailsAdditionalPrebook;
        PRINT 'Cleared existing data to allow data type changes'
        
        -- Alter the columns to correct data types
        BEGIN TRY
            ALTER TABLE ReservationReportDetailsAdditionalPrebook
            ALTER COLUMN MatchedReservationCancellationChargeByPolicy VARCHAR(MAX) NULL;
            PRINT '✅ MatchedReservationCancellationChargeByPolicy changed to VARCHAR(MAX)'
            
            ALTER TABLE ReservationReportDetailsAdditionalPrebook
            ALTER COLUMN MatchedPreBookCancellationChargeByPolicy VARCHAR(MAX) NULL;
            PRINT '✅ MatchedPreBookCancellationChargeByPolicy changed to VARCHAR(MAX)'
            
        END TRY
        BEGIN CATCH
            PRINT '❌ ERROR altering columns: ' + ERROR_MESSAGE()
            PRINT 'You may need to drop and recreate the table'
        END CATCH
    END
    ELSE
    BEGIN
        PRINT '✅ Data types are already correct'
    END
    
    -- Verify the changes
    PRINT ''
    PRINT 'VERIFICATION - Current data types:'
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        CHARACTER_MAXIMUM_LENGTH,
        NUMERIC_PRECISION,
        NUMERIC_SCALE
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ReservationReportDetailsAdditionalPrebook'
      AND COLUMN_NAME IN ('MatchedReservationCancellationChargeByPolicy', 'MatchedPreBookCancellationChargeByPolicy')
    ORDER BY COLUMN_NAME;
    
END
ELSE
BEGIN
    PRINT 'Table ReservationReportDetailsAdditionalPrebook does not exist'
    PRINT 'Please create it using the corrected script: Database/rpndb/dbo/Tables/ReservationReportDetailsAdditionalPrebook.sql'
END

-- Additional verification - check source table data types for reference
PRINT ''
PRINT 'REFERENCE - Source table data types (ReservationTable):'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    NUMERIC_PRECISION,
    NUMERIC_SCALE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ReservationTable'
  AND COLUMN_NAME IN ('MatchedReservationCancellationChargeByPolicy', 'MatchedPreBookCancellationChargeByPolicy')
ORDER BY COLUMN_NAME;

PRINT ''
PRINT '=========================================='
PRINT 'DATA TYPE FIX COMPLETED'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

-- Test the stored procedure after fix
PRINT ''
PRINT 'TESTING STORED PROCEDURE AFTER FIX...'
PRINT 'Executing with RepricerId = 1...'

BEGIN TRY
    EXEC dbo.usp_upd_reservationreport_AdditionalPrebook @Repricerid = 1, @Reservationid = NULL;
    
    DECLARE @ResultCount INT;
    SELECT @ResultCount = COUNT(*) FROM ReservationReportDetailsAdditionalPrebook WHERE Repricerid = 1;
    
    PRINT '✅ Procedure executed successfully!'
    PRINT 'Records created: ' + CAST(@ResultCount AS VARCHAR(10))
    
    IF @ResultCount > 0
    BEGIN
        PRINT ''
        PRINT 'SAMPLE RESULTS:'
        SELECT TOP 3
            ReportId,
            Reservationid,
            PrebookRank,
            prebooksupplier,
            CAST(Profit AS DECIMAL(10,2)) as Profit,
            MatchedReservationCancellationChargeByPolicy,
            MatchedPreBookCancellationChargeByPolicy
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = 1
        ORDER BY Reservationid, PrebookRank;
    END
    
END TRY
BEGIN CATCH
    PRINT '❌ ERROR: ' + ERROR_MESSAGE()
    PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR(10))
    PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR(10))
END CATCH
