import { defineStore } from 'pinia'
import { useApiStore } from './useApiStore';

export const useIrixStore = defineStore({
	// Set persist to true
	persist: false,
	id: "Irix",
	state: () => ({
	}),
	actions: {
		GetGiataRoomMapping(payload) {
			const api = useApiStore();
			const response = api.post(`/api/Irix/GetGiataRoomMapping`, payload);
			return response;
		},
		UpdateRoomMappingStatusInMongoDB(payload) {
			const api = useApiStore();
			const response = api.put(`/api/Irix/UpdateRoomMappingStatusInMongoDB`, payload);
			return response;
		},
	}
});