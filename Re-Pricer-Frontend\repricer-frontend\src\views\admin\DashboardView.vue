<script>
import AllCustomersActiveOptimizations from '@/components/admin/dashboard/AllCustomersActiveOptimizations.vue';
import AllCustomersRealizedOptimizations from '@/components/admin/dashboard/AllCustomersRealizedOptimizations.vue';
import RealizedOptimizationsBreakup from '@/components/admin/dashboard/RealizedOptimizationsBreakup.vue';
import SupplierWiseInformation from '@/components/admin/dashboard/SupplierWiseInformation.vue';
import TotalRealizedOptimizations from '@/components/admin/dashboard/TotalRealizedOptimizations.vue';
import YouCanGainMore from '@/components/admin/dashboard/YouCanGainMore.vue';
import { useRepricerStore } from '@/stores/useRepricerStore';
import { useUserStore } from '@/stores/useUserStore';
import InputSwitch from 'primevue/inputswitch';
import { getUniqueRandomColor } from '@/helpers/utils';
import Calendar from 'primevue/calendar';
import moment from 'moment/moment';
import { useAuthStore } from '@/stores/useAuthStore';
import Button from 'primevue/button';

export default {
    name: "AdminDashboard",
    components: { Button, TotalRealizedOptimizations, AllCustomersRealizedOptimizations, AllCustomersActiveOptimizations, SupplierWiseInformation, RealizedOptimizationsBreakup, YouCanGainMore, InputSwitch, Calendar },
    data() {
        return {
            isLoading: true,
            displayCount: false,
            users: null,
            superAdminSummary: null,
            selectedDate: new Date(),
            maxDate: new Date(),
            isDeveloper: false
        }
    },
    computed: {
        dateRange() {
            if (!this.selectedDate)
                return []
            const date = this.selectedDate;
            return [moment(date).startOf('month').format('YYYY-MM-DD'), moment(date).endOf('month').format('YYYY-MM-DD')];
        }
    },
    methods: {
        async initPage(isFesh) {
            const repricer = useRepricerStore();
            const user = useUserStore();
            const params = {
                "repricerId": 0,
                // "preBookFromDate": this.dateRange[0],
                // "preBookToDate": this.dateRange[1],
            };

            if (isFesh) {
                params["isCached"] = false
            }
            const summary = await repricer.GetSuperAdminReportSummary(params);
            const users = await user.getAllUsers();

            this.superAdminSummary = summary.find(sum => sum.rePricerId == 0);
            const colors = []
            this.users = users.map(user => {
                const customerColor = getUniqueRandomColor(colors);
                colors.push(customerColor);

                const currentRepricerSummary = summary.find(sum => sum.rePricerId == user.rePricerDetail.repricerUserID);
                user["summary"] = currentRepricerSummary;
                user["color"] = customerColor;
                return users
            });
            this.users = users;
        }
    },
    async mounted() {
        // this.initPage();
        const auth = useAuthStore();
        this.isDeveloper = auth.userInfo.roleName.indexOf("Developer") >= 0;
    },
    watch: {
        'dateRange': {
            immediate: true,
            handler(newId, oldId) {
                this.initPage();
            }
        }
    }
}

</script>

<template>
    <main class="container d-flex my-3 flex-column">
        <div class="flex justify-content-end mb-3">
            <Button v-if="isDeveloper" icon="pi pi-refresh" label="Fetch latest data" @click="initPage(true)"></Button>
        </div>
        <div class="mb-4 flex justify-content-end d-none">
            <Calendar v-model="selectedDate" view="month" dateFormat="MM yy" showIcon iconDisplay="input"
                placeholder="Select month" :maxDate="maxDate" />
        </div>
        <div class="card">
            <div class="flex justify-content-between mb-4">
                <h2 class="mb-0">Realized Optimizations</h2>
                <div class="flex gap-2 text-muted">
                    <strong>Amount</strong>
                    <InputSwitch v-model="displayCount" />
                    <strong>Count</strong>
                </div>
            </div>
            <div class="d-flex flex-1 flex-column">
                <AllCustomersRealizedOptimizations :summary="superAdminSummary" :users="users"
                    :displayCount="displayCount" />
                <div
                    class="mt-5 d-flex justify-content-between flex-1 align-items-center gap-5 flex-column flex-lg-row">
                    <TotalRealizedOptimizations :summary="superAdminSummary" :users="users"
                        :displayCount="displayCount" />
                    <RealizedOptimizationsBreakup :summary="superAdminSummary" :users="users"
                        :displayCount="displayCount" />
                </div>
            </div>
        </div>
        <div class="card flex-1">
            <SupplierWiseInformation :users="users" />
        </div>
        <div class="card flex-1">
            <YouCanGainMore :users="users" />
        </div>
    </main>
</template>

<style></style>