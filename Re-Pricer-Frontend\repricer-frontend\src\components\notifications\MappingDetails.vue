<template>
    <div>
        <div v-if="!isEditMode">
            <div class="hotelName lh-sm text-uppercase text-wrap">&nbsp;</div>
            <address class="fw-normal fz-r0_9"><small>&nbsp;</small></address>
        </div>
        <div class="d-flex fz-r0_9 justify-content-between flex-column" :class="[{'border card': isEditMode, 'bg-primary-50': isSelected }]">
            <div class="d-flex fw-600 gap-3 justify-content-between mb-2" v-if="isEditMode">
                {{ mapping.propertyName }}
            </div>
            <div class="d-flex gap-3 justify-content-between">
                <span class="text-muted fz-r0_8">Room</span>
                <span class="max-column-text text-end text-truncate w-10rem" :class="{ 'text-nowrap': !isEditMode }"
                    v-tooltip.bottom="mapping.roomName">{{ mapping.roomName }}</span>
            </div>
            <div class="d-flex gap-3 justify-content-between" v-if="mapping.roomBoard">
                <span class="text-muted fz-r0_8">Room Board</span>
                <span class="max-column-text text-end text-truncate w-10rem" :class="{ 'text-nowrap': !isEditMode }"
                    v-tooltip.bottom="mapping.roomBoard">{{ mapping.roomBoard }}</span>
            </div>
            <div class="d-flex gap-3 justify-content-between">
                <span class="text-muted fz-r0_8">Average Room Type</span>
                <span class="max-column-text text-end text-truncate w-10rem" :class="{ 'text-nowrap': !isEditMode }">{{ mapping.averageRoomType
                    }}</span>
            </div>
            <div class="d-flex gap-3 justify-content-between">
                <span class="text-muted fz-r0_8">Average Room Classes</span>
                <span class="max-column-text text-end text-truncate w-10rem" :class="{ 'text-nowrap': !isEditMode }">{{ mapping.averageRoomClasses
                    }}</span>
            </div>
            <div class="d-flex gap-3 justify-content-between" v-if="mapping.averageRoomViews">
                <span class="text-muted fz-r0_8">Average Room Views</span>
                <span class="max-column-text text-end text-truncate w-10rem" :class="{ 'text-nowrap': !isEditMode }">{{ mapping.averageRoomViews
                    }}</span>
            </div>
            <div class="d-flex gap-3 justify-content-between">
                <span class="text-muted fz-r0_8">Group Name</span>
                <span class="max-column-text text-end text-truncate w-10rem" :class="{ 'text-nowrap': !isEditMode }"
                    v-tooltip.bottom="mapping.groupName">{{ mapping.groupName.toLowerCase() }}</span>
            </div>
            <div class="d-flex gap-3 justify-content-between">
                <span class="text-muted fz-r0_8">Group Confidence</span>
                <span class="max-column-text text-end text-truncate w-10rem" :class="{ 'text-nowrap': !isEditMode }">{{ mapping.groupConfidence
                    }}</span>
            </div>
            <div class="d-flex gap-3 justify-content-between">
                <span class="text-muted fz-r0_8">Bed Detail Description</span>
                <span class="max-column-text text-end text-truncate w-10rem" :class="{ 'text-nowrap': !isEditMode }">{{ mapping.bedDetailDescription
                    }}</span>
            </div>
            <div class="d-flex gap-3 justify-content-between" v-if="mapping.boardMapping">
                <span class="text-muted fz-r0_8">Board</span>
                <span class="max-column-text text-end text-truncate w-10rem" :class="{ 'text-nowrap': !isEditMode }"
                    v-tooltip.bottom="mapping.boardMapping">{{ mapping.boardMapping }}</span>
            </div>
            <div class="d-flex gap-3 justify-content-between" v-if="isEditMode">
                <span class="text-muted fz-r0_8">isActive</span>
                <span class="max-column-text text-end text-truncate w-10rem" :class="{ 'text-nowrap': !isEditMode }">
                    {{mapping.isActive ? 'Active' : 'Disabled'}}
                </span>
            </div>
        </div>
    </div>
</template>

<script>
import Button from 'primevue/button';
import Divider from 'primevue/divider';
import InputSwitch from 'primevue/inputswitch';
import Tag from 'primevue/tag';

export default {
    name: "MappingDetails",
    components: { Divider, InputSwitch, Tag, Button },
    props: ["mappingDetails", "isEditMode", "isSelected"],
    data() {
        return {
        };
    },
    computed: {
        mapping() {
            if (this.mappingDetails) {
                if (Array.isArray(this.mappingDetails)) {
                    return this.mappingDetails[0]
                } else {
                    return this.mappingDetails
                }
            } else {
                return {}
            }
        }
    },
    async mounted() {
    },
    methods: {
    }
};
</script>