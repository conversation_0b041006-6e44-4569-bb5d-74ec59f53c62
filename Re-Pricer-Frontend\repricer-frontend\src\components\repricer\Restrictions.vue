<script>
import { useRepricerStore } from '@/stores/useRepricerStore';
import Listbox from 'primevue/listbox';
import ProgressSpinner from 'primevue/progressspinner';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Divider from 'primevue/divider';
import Card from 'primevue/card';
import Chip from 'primevue/chip';
import { useAuthStore } from '@/stores/useAuthStore';
import Message from 'primevue/message';

export default {
    props: ["createRepricerPayload"],
    emits: [],
    components: { Listbox, ProgressSpinner, Button, InputText, Divider, Card, Chip, Message },
    data() {
        return {
            createRepricerPayload: null,
            isConfigUpdating: false,
            isDeveloper: false,

            resellerList: [],
            disabledResellerList: [],
            resellerFilter: "",

            supplierList: [],
            disabledSupplierList: [],
            supplierFilter: "",

            countryList: [],
            disabledCountryList: [],
            countryFilter: "",
            isCountryLoading: true,
            selectedCountry: null,

            cityList: [],
            disabledCityList: [],
            cityFilter: "",
            isCityLoading: false,
            selectedCity: null,

            hotelList: [],
            disabledHotelList: [],
            hotelFilter: "",
            isHotelsLoading: false,
            selectedHotel: null
        };
    },
    computed: {
        countryListFiltered() {
            return this.countryList.filter((country) => {
                return country.countryName?.toLowerCase().includes(this.countryFilter.toLowerCase()) &&
                    !this.disabledCountryList.some(disabledCountry => disabledCountry.countryId === country.countryId);
            });
        },
        cityListFiltered() {
            return this.cityList.filter((city) => {
                return city.cityName?.toLowerCase().includes(this.cityFilter.toLowerCase()) &&
                    !this.disabledCityList.some(disabledCity => disabledCity.cityId === city.cityId);
            });
        },
        hotelListFiltered() {
            return this.hotelList.filter((hotel) => {
                return hotel.hotelName?.toLowerCase().includes(this.hotelFilter.toLowerCase()) &&
                    !this.disabledHotelList.some(disabledHotel => disabledHotel.hotelId === hotel.hotelId);
            });
        },
        resellerListFiltered() {
            return this.resellerList
                .filter((reseller) => {
                    return reseller.resellerName?.toLowerCase().includes(this.resellerFilter.toLowerCase()) &&
                        !this.disabledResellerList.some(disabledReseller => disabledReseller.resellerId === reseller.resellerId);
                })
                .sort((a, b) => a.resellerName?.localeCompare(b.resellerName));
        },
        supplierListFiltered() {
            return this.supplierList
                .filter((supplier) => {
                    return supplier.supplierName?.toLowerCase().includes(this.supplierFilter.toLowerCase()) &&
                        !this.disabledSupplierList.some(disabledSupplier => disabledSupplier.supplierName === supplier.supplierName);
                })
                .sort((a, b) => a.supplierName?.localeCompare(b.supplierName));
        }
    },
    methods: {
        async getCountry() {
            const repricer = useRepricerStore();
            this.countryList = await repricer.getCountryList(this.createRepricerPayload?.repricerUserID);
            this.isCountryLoading = false;
        },
        async getReseller() {
            const repricer = useRepricerStore();
            this.resellerList = await repricer.getResellerList(this.createRepricerPayload?.repricerUserID);
        },
        async getSupplier() {
            const repricer = useRepricerStore();
            this.supplierList = await repricer.getSupplierList(this.createRepricerPayload?.repricerUserID);
        },
        async getCitiesByCountryId(data) {
            if (!data) return;
            this.selectedCity = null;
            this.isCityLoading = true;
            this.cityList = [];
            this.hotelList = [];
            const repricer = useRepricerStore();
            this.selectedCountry = data.countryId;
            this.cityFilter = "";
            this.hotelFilter = "";
            this.cityList = await repricer.getCityList(data.repricerId, data.countryId);
            this.isCityLoading = false;
        },
        async getHotelsByCityId(data) {
            if (!data) return;
            this.selectedHotel = null;
            this.isHotelsLoading = true;
            this.hotelList = [];
            const repricer = useRepricerStore();
            this.selectedCity = data.cityId;
            this.hotelFilter = "";
            this.hotelList = await repricer.getCityHotel(data.repricerId, this.selectedCountry, data.cityId);
            this.isHotelsLoading = false;
        },
        disableThisSupplier(supplier) {
            if (supplier) {
                this.disabledSupplierList.push(supplier);
            }
        },
        enableThisSupplier(supplier) {
            if (supplier) {
                this.disabledSupplierList = this.disabledSupplierList.filter(
                    (disabledSupplier) => disabledSupplier.supplierName !== supplier.supplierName
                );
            }
        },
        async updateSupplierRestrictions() {
            this.createRepricerPayload.restrictedSupplier = this.disabledSupplierList;
            this.updateRepricer();
        },
        disableThisReseller(reseller) {
            if (reseller) {
                this.disabledResellerList.push(reseller);
            }
        },
        enableThisReseller(reseller) {
            if (reseller) {
                this.disabledResellerList = this.disabledResellerList.filter(
                    (disabledReseller) => disabledReseller.resellerId !== reseller.resellerId
                );
            }
        },
        async updateResellerRestrictions() {
            this.createRepricerPayload.restrictedReseller = this.disabledResellerList;
            this.updateRepricer();
        },
        disableThisCity(city) {
            if (city) {
                this.disabledCityList.push(city);
            }
        },
        enableThisCity(city) {
            if (city) {
                this.disabledCityList = this.disabledCityList.filter(
                    (disabledCity) => disabledCity.cityId !== city.cityId
                );
            }
        },
        async updateCityRestrictions() {
            this.createRepricerPayload.restrictedCity = this.disabledCityList;
            this.updateRepricer();
        },
        disableThisCountry(country) {
            if (country) {
                this.disabledCountryList.push(country);
            }
        },
        enableThisCountry(country) {
            if (country) {
                this.disabledCountryList = this.disabledCountryList.filter(
                    (disabledCountry) => disabledCountry.countryId !== country.countryId
                );
            }
        },
        async updateCountryRestrictions() {
            this.createRepricerPayload.restrictedCountry = this.disabledCountryList;
            this.updateRepricer();
        },
        disableThisHotel(hotel) {
            if (hotel) {
                this.disabledHotelList.push(hotel);
            }
        },
        enableThisHotel(hotel) {
            if (hotel) {
                this.disabledHotelList = this.disabledHotelList.filter(
                    (disabledHotel) => disabledHotel.hotelId !== hotel.hotelId
                );
            }
        },
        async updateHotelRestrictions() {
            this.createRepricerPayload.restrictedHotel = this.disabledHotelList;
            this.updateRepricer();
        },
        async updateRepricer() {
            this.isConfigUpdating = true;
            const rePricer = useRepricerStore();
            const response = await rePricer.updateRepricer(this.createRepricerPayload);
            this.isConfigUpdating = false;
            if (!response.isError) {
                this.createRepricerPayload = response.data;
                this.$toast.add({ severity: 'success', summary: 'Repricer update done.', detail: response.message, life: 3000 });
            } else {
                this.$toast.add({ severity: 'error', summary: 'Repricer update failed.', detail: response.message, life: 3000 });
            }
        },
    },
    async mounted() {
        const auth = useAuthStore();
        this.isDeveloper = auth.userInfo.roleName.indexOf("Developer") >= 0;

        const rePricer = useRepricerStore();
        const repricerID = this.$route.params.repricerId || rePricer.repricer.repricerUserID;
        this.createRepricerPayload = await rePricer.getRePricerById(repricerID);

        this.disabledSupplierList = this.createRepricerPayload?.restrictedSupplier || [];
        this.disabledResellerList = this.createRepricerPayload?.restrictedReseller || [];
        this.disabledCountryList = this.createRepricerPayload?.restrictedCountry || [];
        this.disabledCityList = this.createRepricerPayload?.restrictedCity || [];
        this.disabledHotelList = this.createRepricerPayload?.restrictedHotel || [];

        if (this.createRepricerPayload) {
            this.getCountry();
            this.getReseller();
            this.getSupplier();
        }
    },
    watch: {
    }
};
</script>

<template>
    <main class="container my-3 min-vh-100 flex flex-column gap-4">
        <div class="d-flex justify-content-between align-items-center">
            <div class="fw-500 fz-r1_7 ">Restrictions</div>
            <Message severity="info">Please click the <b>"Update"</b> button after making changes in the configurations to save them in the database.</Message>
        </div>
        <Card>
            <template #title><i class="pi pi-sitemap"></i> Reseller</template>
            <template #subtitle>Disabling the reseller will deactivate all reservations associated with its country, city, and hotels.</template>
            <template #content>
                <div class="row">
                    <div class="col-4">
                        <div class="mb-3">
                            <InputText type="text" v-model="resellerFilter" class="w-100" />
                        </div>
                        <div class="h-25rem overflow-y-auto"
                            :class="{ 'align-items-center flex justify-content-center': (resellerList ?? []).length == 0 }">
                            <template v-if="!(resellerList ?? []).length && !isResellerLoading">
                                <ProgressSpinner />
                            </template>
                            <div v-else-if="!(resellerList ?? []).length">Reseller not found</div>
                            <template v-else>
                                <div v-for="reseller in (resellerList ?? []).filter(reseller => reseller?.resellerName?.toLowerCase().includes(resellerFilter.toLowerCase()))"
                                    class="align-items-center border-bottom flex justify-content-between p-2 hover:bg-gray-50">
                                    <div>
                                        <div v-html="reseller?.resellerName"></div>
                                        <small class="font-italic text-muted">{{ reseller?.resellerId }} - {{
                                            reseller?.resellerCode }}</small>
                                    </div>
                                    <div>
                                        <Button icon="pi pi-ban" text rounded @click="disableThisReseller(reseller)" title="Disable Reseller"
                                            severity="secondary" />
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="col-8 flex flex-column gap-3">
                        <div class="border flex-1 p-3 d-flex flex-column">
                            <div class="fw-bold fz-r1_1">Disabled Reseller</div>
                            <div class="flex flex-wrap gap-2 mt-2"
                                :class="{ 'flex-1': (disabledResellerList ?? []).length == 0 }">
                                <div v-if="!(disabledResellerList ?? []).length" class="align-self-center m-auto">
                                    All resellers available for optimization.
                                </div>
                                <Chip :label="reseller?.resellerName" v-for="reseller in (disabledResellerList ?? [])"
                                    class="cursor-pointer" key="reseller?.resellerId"
                                    @click="enableThisReseller(reseller)">
                                    {{ reseller?.resellerName }} <i class="pi pi-times-circle ms-2"></i>
                                </Chip>
                            </div>
                        </div>

                    </div>
                </div>
            </template>
            <template #footer>
                <Button class="d-flex ms-auto" label="Update Reseller Restrictions" severity="secondary"
                    :loading="isConfigUpdating" @click="updateResellerRestrictions()" />
            </template>
        </Card>

        <Card v-if="isDeveloper">
            <template #title><i class="pi pi-share-alt"></i> Supplier</template>
            <template #content>
                <div class="row">
                    <div class="col-4">
                        <div class="mb-3">
                            <InputText type="text" v-model="supplierFilter" class="w-100" />
                        </div>
                        <div class="h-25rem overflow-y-auto" :class="{ 'align-items-center flex justify-content-center': supplierList.length == 0 }">
                            <template v-if="supplierList.length == 0">
                                <div v-if="isSupplierLoading">
                                    <ProgressSpinner />
                                </div>
                                <div v-else>
                                    No supplier found
                                </div>
                            </template>
                            <template v-else>
                                <div v-for="supplier in supplierListFiltered"
                                    class="align-items-center border-bottom flex justify-content-between p-2 hover:bg-gray-50">
                                    <div>
                                        <div v-html="supplier.supplierName"></div>
                                        <small class="font-italic text-muted">Active: {{ supplier.isActive }}</small>
                                    </div>
                                    <div>
                                        <Button icon="pi pi-ban" text rounded @click="disableThisSupplier(supplier)" title="Disable Supplier"
                                            severity="secondary" />
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="col-8 flex flex-column gap-3">
                        <div class="border flex-1 p-3 d-flex flex-column">
                            <div class="fw-bold fz-r1_1">Disabled Suppliers</div>
                            <div class="flex flex-wrap gap-2 mt-2" :class="{ 'flex-1': disabledSupplierList.length == 0 }">
                                <div v-if="disabledSupplierList.length == 0" class="align-self-center m-auto">
                                    All suppliers available for optimization.
                                </div>
                                <Chip v-for="supplier in disabledSupplierList" class="cursor-pointer" key="supplier"
                                    @click="enableThisSupplier(supplier)">
                                    {{ supplier.supplierName }} <i class="pi pi-times-circle ms-2"></i>
                                </Chip>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #footer>
                <Button class="d-flex ms-auto" label="Update Suppliers Restrictions" severity="secondary"
                    :loading="isConfigUpdating" @click="updateSupplierRestrictions()" />
            </template>
        </Card>

        <div class="row">
            <div class="col-4">
                <Card>
                    <template #title><i class="pi pi-map"></i> Country</template>
                    <template #subtitle>
                        <div class="m-height-80px">
                            System will not optimize reservations from the selected countries. <br>
                            If a country is selected, all its cities and hotels will be automatically disabled.
                        </div>
                    </template>
                    <template #content>
                        <div></div>
                        <div class="my-3">
                            <InputText type="text" v-model="countryFilter" class="w-100" />
                        </div>
                        <div class="h-25rem overflow-y-auto"
                            :class="{ 'align-items-center flex justify-content-center': countryList.length == 0 }">
                            <template v-if="countryList.length == 0">
                                <div v-if="isCountryLoading">
                                    <ProgressSpinner />
                                </div>
                                <div v-else>
                                    <div>All country found</div>
                                </div>
                            </template>
                            <template v-else>
                                <div v-for="country in countryListFiltered"
                                    :class="{ 'bg-primary-100': country.countryId == selectedCountry, 'hover:bg-gray-50': country.countryId != selectedCountry }"
                                    class="align-items-center border-bottom flex justify-content-between p-2">
                                    <div>
                                        <div v-html="country.countryName"></div>
                                        <small class="font-italic text-muted">{{ country.countryId }}</small>
                                    </div>
                                    <div>
                                        <Button icon="pi pi-ban" text rounded @click="disableThisCountry(country)" title="Disable Country"
                                            severity="secondary" />
                                        <Button icon="pi pi-angle-double-right" text rounded
                                            @click="getCitiesByCountryId(country)" title="Select City"
                                            severity="secondary" />
                                    </div>
                                </div>
                            </template>

                        </div>

                        <div class="flex flex-column gap-3 h-25rem mt-3">
                            <div class="border flex-1 p-3 d-flex flex-column">
                                <div class="fw-bold fz-r1_1">Disabled Countries</div>
                                <div class="flex flex-wrap gap-2 mt-2" :class="{ 'flex-1': disabledCountryList.length == 0 }">
                                    <div v-if="disabledCountryList.length == 0" class="align-self-center m-auto">
                                        <div>All countries available for optimization.</div>
                                    </div>
                                    <Chip v-for="country in disabledCountryList" class="cursor-pointer"
                                        key="city.cityId" @click="enableThisCountry(country)">
                                        {{ country.countryName }} <i class="pi pi-times-circle ms-2"></i>
                                    </Chip>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #footer>
                        <Button class="d-flex ms-auto" label="Update Country Restrictions" severity="secondary"
                            @click="updateCountryRestrictions()" :loading="isConfigUpdating" />
                    </template>
                </Card>
            </div>
            <div class="col-4">
                <Card>
                    <template #title><i class="pi pi-map-marker"></i> Cities</template>
                    <template #subtitle>
                        <div class="m-height-80px">
                            System will not optimize reservations from the selected cities. <br>
                            If a city is selected, all its hotels will be automatically disabled.
                        </div>
                    </template>
                    <template #content>
                        <div class="my-3">
                            <InputText type="text" v-model="cityFilter" class="w-100" />
                        </div>
                        <div class="h-25rem overflow-y-auto"
                            :class="{ 'align-items-center flex justify-content-center': cityList.length == 0 }">
                            <div v-if="isCityLoading">
                                <ProgressSpinner />
                            </div>
                            <template v-else>
                                <template v-if="cityList.length == 0">
                                    <div v-if="selectedCountry">No city found</div>
                                    <div v-else>Please select country.</div>
                                </template>
                                <template v-else>
                                    <div v-for="city in cityListFiltered"
                                        :class="{ 'bg-primary-100': city.cityId == selectedCity, 'hover:bg-gray-50': city.cityId != selectedCity }"
                                        class="align-items-center border-bottom flex justify-content-between p-2">
                                        <div>
                                            <div v-html="city.cityName"></div>
                                            <small class="font-italic text-muted">{{ city.cityId }}</small>
                                        </div>
                                        <div>
                                            <Button icon="pi pi-ban" text rounded @click="disableThisCity(city)" title="Disable City"
                                                severity="secondary" />
                                            <Button icon="pi pi-angle-double-right" text rounded
                                                @click="getHotelsByCityId(city)" title="Select Hotel"
                                                severity="secondary" />
                                        </div>
                                    </div>
                                </template>
                            </template>
                        </div>

                        <div class="flex flex-column gap-3 h-25rem mt-3">
                            <div class="border flex-1 p-3 d-flex flex-column">
                                <div class="fw-bold fz-r1_1">Disabled Cities</div>
                                <div class="flex flex-wrap gap-2 mt-2" :class="{ 'flex-1': disabledCityList.length == 0 }">
                                    <div v-if="disabledCityList.length == 0" class="align-self-center m-auto">
                                        All cities available for optimization.
                                    </div>
                                    <Chip v-for="city in disabledCityList" class="cursor-pointer" key="city.cityId"
                                        @click="enableThisCity(city)">
                                        {{ city.cityName }} <i class="pi pi-times-circle ms-2"></i>
                                    </Chip>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #footer>
                        <Button class="d-flex ms-auto" label="Update City Restrictions" severity="secondary"
                            @click="updateCityRestrictions()" :loading="isConfigUpdating" />
                    </template>
                </Card>

            </div>
            <div class="col-4">
                <Card>
                    <template #title><i class="pi pi-building"></i> Hotels</template>
                    <template #subtitle>
                        <div class="m-height-80px">
                            System will not optimize reservations from the selected hotels. <br>
                            If a hotel is selected, it will be automatically disabled.
                        </div>
                    </template>
                    <template #content>
                        <div class="my-3">
                            <InputText type="text" v-model="hotelFilter" class="w-100" />
                        </div>
                        <div class="h-25rem overflow-y-auto"
                            :class="{ 'align-items-center flex justify-content-center': hotelList.length == 0 }">
                            <div v-if="isHotelsLoading">
                                <ProgressSpinner />
                            </div>
                            <template v-else>
                                <template v-if="hotelList.length == 0">
                                    <div v-if="selectedCity">No hotel found</div>
                                    <div v-else>Please select city.</div>
                                </template>
                                <template v-else>
                                    <div v-for="hotel in hotelListFiltered"
                                        class="align-items-center border-bottom flex justify-content-between p-2 hover:bg-gray-50">
                                        <div>
                                            <div v-html="hotel.hotelName"></div>
                                            <small class="font-italic text-muted">{{ hotel.hotelId }}</small>
                                        </div>
                                        <div>
                                            <Button icon="pi pi-ban" text rounded severity="secondary" title="Disable Hotel"
                                                @click="disableThisHotel(hotel)" />
                                        </div>
                                    </div>
                                </template>
                            </template>
                        </div>


                        <div class="flex flex-column gap-3 h-25rem mt-3">
                            <div class="border flex-1 p-3 d-flex flex-column">
                                <div class="fw-bold fz-r1_1">Disabled Hotels</div>
                                <div class="flex flex-wrap gap-2 mt-2" :class="{ 'flex-1': disabledHotelList.length == 0 }">
                                    <div v-if="disabledHotelList.length == 0" class="align-self-center m-auto">
                                        All hotels available for optimization.
                                    </div>
                                    <Chip v-for="hotel in disabledHotelList" class="cursor-pointer" key="city.cityId"
                                        @click="enableThisHotel(hotel)">
                                        {{ hotel.hotelName }} <i class="pi pi-times-circle ms-2"></i>
                                    </Chip>
                                </div>
                            </div>

                        </div>
                    </template>
                    <template #footer>
                        <Button class="d-flex ms-auto" label="Update Hotel Restrictions" severity="secondary"
                            @click="updateHotelRestrictions()" :loading="isConfigUpdating" />
                    </template>
                </Card>

            </div>
        </div>
    </main>
</template>

<style scoped>
    .m-height-80px {
        min-height: 80px;
    }
</style>
