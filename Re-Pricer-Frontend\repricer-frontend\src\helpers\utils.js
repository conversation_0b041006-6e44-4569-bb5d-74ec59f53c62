import * as XLSX from "xlsx";

const getCookie = (cookieName) => {
	var name = cookieName + "=";
	var decodedCookie = decodeURIComponent(document.cookie);
	var cookieArray = decodedCookie.split(';');
	for (var i = 0; i < cookieArray.length; i++) {
		var cookie = cookieArray[i].trim();
		if (cookie.indexOf(name) == 0) {
			return cookie.substring(name.length, cookie.length);
		}
	}
	return ""; // Return an empty string if the cookie is not found
}

const setCookie = (cookieName, cookieValue, expirationDays) => {
	var d = new Date();
	d.setTime(d.getTime() + (expirationDays * 24 * 60 * 60 * 1000));
	var expires = "expires=" + d.toUTCString();
	document.cookie = cookieName + "=" + cookieValue + ";" + expires + ";path=/";
}

const setCookieWithTime = (cookieName, cookieValue, expirationTime) => {
	var expires = "expires=" + expirationTime;
	document.cookie = cookieName + "=" + cookieValue + ";" + expires + ";path=/";
}

const deleteCookie = (name) => {
	document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}

const decodeJWT = (token) => {
	const base64Url = token.split('.')[1];
	const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
	const payload = decodeURIComponent(atob(base64).split('').map((c) => {
		return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
	}).join(''));

	return JSON.parse(payload);
}

const showErrorMessage = (toast, response, title) => {
	let errorMsgs = [];
	if (typeof (response.data) == "string") {
		errorMsgs.push(response.data)
	} else {
		const errors = response.errors || response.data.errors || response.data;
		Object.keys(errors).map(key => {
			errorMsgs = errorMsgs.concat(errors[key])
		})
	}
	toast.add({ severity: 'error', summary: title, detail: errorMsgs.join(' '), life: 30000 });
}

const matchedCronTime = (cronTime) => {
	// Split the CRON time into its components
	const [cronMinute, cronHours] = cronTime.split(' ');
	const cronHourList = cronHours.split(',');

	// Get the current date and time
	const now = new Date();
	const currentMinute = now.getMinutes();
	const currentHour = now.getHours();

	// Check if the current time matches the CRON job time
	if (currentMinute === parseInt(cronMinute) && cronHourList.includes(currentHour.toString())) {
		return true;
	}

	return false;
}

const getUserTimezone = () => {
	const offsetMinutes = new Date().getTimezoneOffset();
	const offsetHours = -offsetMinutes / 60;
	const sign = offsetHours >= 0 ? "+" : "-";
	const absoluteOffset = Math.abs(offsetHours);

	// Format the offset to match UTC format (e.g., +05:30, -03:00)
	const hours = String(Math.floor(absoluteOffset)).padStart(2, '0');
	const minutes = String((absoluteOffset % 1) * 60).padStart(2, '0');

	return `UTC${sign}${hours}:${minutes}`;
}

const getUniqueRandomColor = (existingColors) => {
	let color;
	do {
		const r = Math.floor(Math.random() * 256);
		const g = Math.floor(Math.random() * 256);
		const b = Math.floor(Math.random() * 256);
		const a = 0.7;  // Set a constant alpha
		color = `rgba(${r}, ${g}, ${b}, ${a})`;
	} while (existingColors.includes(color)); // Ensure it's unique

	return color;
}

const flattenJSON = (data) => {
	const result = data.map((item) => {
	  const flattened = {};
  
	  const flatten = (obj, prefix = "") => {
		for (const key in obj) {
		  const value = obj[key];
		  const newKey = prefix ? `${prefix}.${key}` : key;
  
		  // Check if the value is an array
		  if (Array.isArray(value)) {
			// If array elements are objects, flatten each
			if (value.some((elem) => typeof elem === "object" && elem !== null)) {
			  value.forEach((elem, index) => {
				if (typeof elem === "object" && elem !== null) {
				  flatten(elem, `${newKey}[${index}]`);
				} else {
				  flattened[`${newKey}[${index}]`] = elem;
				}
			  });
			} else {
			  // For primitive arrays, join them or assign directly
			  flattened[newKey] = value.join(", ");
			}
		  } else if (typeof value === "object" && value !== null) {
			// Recursively flatten nested objects
			flatten(value, newKey);
		  } else {
			flattened[newKey] = value;
		  }
		}
	  };
  
	  flatten(item);
	  return flattened;
	});
  
	return result;
  };
  
const downloadJSONAsExcel = (data, filename = "excel_file", requiredFields, invoiceInfo) => {
	const customerNameMapping = {};

	if (invoiceInfo) {
		invoiceInfo.map(invoice => {
			customerNameMapping[invoice.id] = invoice.rePricerDetail.repricerUserName;
			return invoice;
		});
	}

	// Flatten JSON data
	const flattenedData = flattenJSON(data);

	if (requiredFields) {
		flattenedData.map(row => {
			if (invoiceInfo) {
				row['invoiceable'] = row.optimizationProfit / 2;
				row['repricerName'] = customerNameMapping[row.repricerId];
			}
			Object.keys(row).map(key => {
				if (requiredFields.indexOf(key) < 0) {
					delete row[key];
				}
				return key;
			});
			return row;
		});
	}

	if (invoiceInfo) {
		// Calculate total count
		const totalCount = flattenedData.length;
		const totalProfit = flattenedData.reduce((acc, r) => acc + r.optimizationProfit, 0).toFixed(2);
		const totalInvoice = flattenedData.reduce((acc, r) => acc + r.invoiceable, 0).toFixed(2);
	
		// Add a summary row
		if (flattenedData.length > 0) {
			["Total Reservation Count", "Total Realized Gain", "Total Invoiceable"].map(key => {
			// Prepare a summary row with the same structure as the data
			const summaryRow = {};
	
			// Add "Total Count" to a specific column (e.g., a column called 'Summary' or the first key)
			const firstKey = Object.keys(flattenedData[0])[0]; // First key in the JSON
			summaryRow[firstKey] = key;
	
			// Add the total count value in another column, e.g., the last column
			const lastKey = Object.keys(flattenedData[0])[1]; // Last key in the JSON
	
			switch (key) {
				case "Total Reservation Count":
					summaryRow[lastKey] = totalCount;
					break;
				case "Total Realized Gain":
					summaryRow[lastKey] = totalProfit;
					break;
				case "Total Invoiceable":
					summaryRow[lastKey] = totalInvoice;
					break;
			}
			// Fill other columns with empty strings
			Object.keys(flattenedData[0]).forEach(key => {
				if (!summaryRow[key]) {
					summaryRow[key] = "";
				}
			});
	
			// Add the summary row to the data
			flattenedData.push(summaryRow);
				return key
			})
		}
	}

	let worksheet = null;
	if (!invoiceInfo) {
		// Step 1: Convert JSON to worksheet
		const friendlyHeaders = {
			"repricerId": "Repricer ID",
			"reservationId": "Reservation ID",
			"bookingDate": "Booking Date",
			"createdDate": "Created Date",
			"profitAfterCancellation": "Profit After Cancellation",
			"optimizationProfit": "Optimization Profit",
			"cpStatus": "CP Status",
			"cpDaysGain": "CP Days Gain",
			"matchedCancellationPolicyGain": "Matched CP Gain",
			"currency": "Currency",
			"reservation.checkIn": "Check-in Date",
			"reservation.checkOut": "Check-out Date",
			"reservation.adultCount": "Adults Count",
			"reservation.childAges": "Children Ages",
			"reservation.roomName": "Room Name",
			"reservation.roomBoard": "Room Board",
			"reservation.hotelName": "Hotel Name",
			"reservation.destination": "Destination",
			"reservation.cancellationPolicyStartDate": "Cancellation Policy Start Date",
			"prebook.price": "Prebook Price",
			"prebook.cancellationPolicyStartDate": "Prebook CP Start Date",
			"resellerDetail.resellerCode": "Reseller Code",
			"resellerDetail.resellerType": "Reseller Type"
		};
		
		function processFlattenedData(flattenedData) {
			return flattenedData.map(item => {
				let formattedItem = {};
				for (let key in item) {
					if (key.startsWith("reservation.cancellationPoliciesBySource") || key.startsWith("prebook.cancellationPoliciesBySource")) continue;
					formattedItem[friendlyHeaders[key] || key] = item[key];
				}
				Object.assign(formattedItem, extractCancellationPolicies(item, "reservation"));
				Object.assign(formattedItem, extractCancellationPolicies(item, "prebook"));
				return formattedItem;
			});
		}
		
		function extractCancellationPolicies(item, type) {
			let policies = {};
			let index = 0;
			while (item[`${type}.cancellationPoliciesBySource[${index}].source`]) {
				let policyType = item[`${type}.cancellationPoliciesBySource[${index}].source`];
				let policyKey = `${type.toUpperCase()} - ${policyType} Policy`;
				let policyData =
					`Start: ${item[`${type}.cancellationPoliciesBySource[${index}].cancellationPolicyStartDate`]}` +
					`\nCharge: ${item[`${type}.cancellationPoliciesBySource[${index}].cancellationCharge`]} ${item[`${type}.cancellationPoliciesBySource[${index}].currency`]}` +
					`\nType: ${item[`${type}.cancellationPoliciesBySource[${index}].cancellationPolicyType`]}`;
				
				policies[policyKey] = policies[policyKey] ? policies[policyKey] + "\n\n" + policyData : policyData;
				index++;
			}
			return policies;
		}
		
		const transformedData = processFlattenedData(flattenedData);
		worksheet = XLSX.utils.json_to_sheet(transformedData, { cellStyles: true });

		const wscols = Object.keys(transformedData[0]).map(key => ({ wch: 30 }));
		worksheet['!cols'] = wscols;

		// Set text wrapping for all cells
		if (!worksheet['!rows']) worksheet['!rows'] = [];
		for (let i = 0; i < transformedData.length; i++) {
			worksheet['!rows'][i] = { hpx: 40 }; // Adjust row height to ensure multiline display
		}
	} else {
		worksheet = XLSX.utils.json_to_sheet(flattenedData, { cellStyles: true });
		const wscols = Object.keys(flattenedData[0]).map(key => ({ wch: 15 }));
		worksheet['!cols'] = wscols;
	}

	// Step 2: Create a new workbook and append the worksheet
	const workbook = XLSX.utils.book_new();
	XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

	// Step 3: Write the workbook to a binary string and trigger download
	XLSX.writeFile(workbook, `${filename}.xlsx`);
};



export {
	getCookie,
	setCookie,
	setCookieWithTime,
	deleteCookie,
	decodeJWT,
	showErrorMessage,
	matchedCronTime,
	getUserTimezone,
	getUniqueRandomColor,
	downloadJSONAsExcel
}