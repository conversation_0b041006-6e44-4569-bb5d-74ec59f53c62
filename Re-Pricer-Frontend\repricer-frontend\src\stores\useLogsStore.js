import { defineStore } from 'pinia'
import { useApiStore } from './useApiStore';

export const useLogsStore = defineStore({
	// Set persist to true
	persist: true,
	id: "logs",
	state: () => ({
	}),
	actions: {
		async ApiHitCounts(payload) {
			const api = useApiStore();
			
			const params = [];
			Object.keys(payload).map((key) => {
				params.push(`${key}=${payload[key]}`)
				return key;
			});

			return await api.get(`/api/Logging/ApiHitCounts?${params.join('&')}`);
		},
	}
});