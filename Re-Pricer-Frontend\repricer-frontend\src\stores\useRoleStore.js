import { defineStore } from 'pinia'
import { useApiStore } from './useApiStore';

export const useRoleStore = defineStore({
	// Set persist to true
	persist: true,
	id: "role",
	state: () => ({
		roles: [],
	}),
	actions: {
		async getAllRoles() {
			const api = useApiStore();
			const response = await api.get("/api/RoleManager/AllRoles");
			if (response) {
				this.roles = response
			}
			return response;
		},
		async createRole(roleInfo) {
			const api = useApiStore();
			const response = await api.post("/api/RoleManager/CreateRole", roleInfo);
			return response;
		},
		async updateRole(roleInfo) {
			const api = useApiStore();
			const response = await api.put("/api/RoleManager/EditRole", roleInfo);
			return response;
		},
		async deleteRole(id) {
			const api = useApiStore();
			const response = await api.remove(`/api/RoleManager/DeleteRole/${id}`);
			return response;
		},
	}
});