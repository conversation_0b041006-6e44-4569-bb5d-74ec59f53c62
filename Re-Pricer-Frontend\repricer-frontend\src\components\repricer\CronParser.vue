<template>
    <div class="cron-converter">
        <small v-if="error" class="text-danger">{{ error }}</small>
        <small v-if="result && !error" v-html="result"></small>
    </div>
</template>

<script>
export default {
    props: ['cronExpr'],
    emits: ['onComplete'],
    data() {
        return {
            result: '',
            error: ''
        };
    },
    methods: {
        convertCron() {
            this.error = '';
            this.result = '';
            if (this.validateCron(this.cronExpr)) {
                this.result = this.convertCronToHumanReadable(this.cronExpr);
            }
        },
        validateCron(cronExpr) {
            const cronRegex = /(@(annually|yearly|monthly|weekly|daily|hourly|reboot))|(@every (\d+(ns|us|µs|ms|s|m|h)+))|((\d+|\*|\*\/\d+|(\d+(,\d+)+)|(\d+([-\/])\d+)){1} ?){5}/;
            if (!cronRegex.test(cronExpr)) {
                this.error = 'Invalid CRON expression';
                this.$emit('onComplete', false);
                return false;
            }
            return true;
        },
        cronToString(cronExpr) {
            const parts = cronExpr.split(' ');
            const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;

            const parseField = (field, names, fromArray) => {
                if (field === '*') {
                    return 'all';
                }
                if (field.includes('/')) {
                    const [start, step] = field.split('/');
                    return `all ${step} ${names ? names[0].toLowerCase() : 'units'} starting at ${start}`;
                }
                if (field.includes('-')) {
                    const [start, end] = field.split('-');
                    if (fromArray) {
                        return `from ${names[start]} to ${names[end]}`;
                    }
                    return `from ${start} to ${end}`;
                }
                if (field.includes(',')) {
                    return field.split(',').map(f => (names ? names[parseInt(f, 10)] : f)).join(', ');
                }
                if (!isNaN(field)) {
                    return names ? names[parseInt(field, 10)] : field;
                }
                return field;
            };

            const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            const monthsOfYear = ['undefined', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

            const minuteStr = parseField(minute) + (minute === '1' ? ' minute' : ' minutes');
            const hourStr = parseField(hour) + (hour === '1' ? ' hour' : ' hours');
            const dayOfMonthStr = parseField(dayOfMonth) + (dayOfMonth === '1' ? ' day' : ' days');
            const monthStr = parseField(month, monthsOfYear, true) + (month === '1' ? ' month' : ' months');
            const dayOfWeekStr = parseField(dayOfWeek, daysOfWeek) + (dayOfWeek === '1' ? ' day' : ' days');

            const output = `At ${minuteStr} past ${hourStr} on ${dayOfMonthStr} of ${monthStr} and on ${dayOfWeekStr}`;

            if (output.indexOf('undefined') < 0) {
                return `At ${minuteStr} past ${hourStr} on ${dayOfMonthStr} of ${monthStr} and on ${dayOfWeekStr}`;
            } else {
                this.error = 'Invalid CRON expression';
                this.$emit('onComplete', false);
                return false;
            }
        },
        convertCronToHumanReadable(cronExpression) {
            const [minute, hour, dayOfMonth, month, dayOfWeek] = cronExpression.split(' ');

            const getMonthName = (month) => {
                const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                return month === '*' ? 'every month' : months[parseInt(month) - 1];
            };

            const getDayOfWeekName = (day) => {
                const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                return day === '*' ? 'every day' : days[parseInt(day)];
            };

            const getTimeString = (hour, minute) => {
                const normalizedHour = hour % 12 || 12; // Convert to 12-hour format
                const period = hour < 12 ? 'AM' : 'PM';
                return `${String(normalizedHour).padStart(2, '0')}:${String(minute).padStart(2, '0')} ${period}`;
            };

            const timeString = hour.split(',').map(h => getTimeString(parseInt(h), minute)).join(' and ');

            let result = `The job runs at ${timeString}`;

            if (dayOfMonth !== '*' && dayOfWeek === '*') {
                result += ` on day ${dayOfMonth} of ${getMonthName(month)}`;
            } else if (dayOfWeek !== '*' && dayOfMonth === '*') {
                result += ` on ${getDayOfWeekName(dayOfWeek)} of ${getMonthName(month)}`;
            } else if (dayOfMonth !== '*' && dayOfWeek !== '*') {
                result += ` on day ${dayOfMonth} and ${getDayOfWeekName(dayOfWeek)} of ${getMonthName(month)}`;
            } else {
                result += ` every day of ${getMonthName(month)}`;
            }

            return result;
        }
    },
    mounted() {
        this.convertCron();
    },
    watch: {
        cronExpr() {
            this.convertCron();
        }
    }
};
</script>