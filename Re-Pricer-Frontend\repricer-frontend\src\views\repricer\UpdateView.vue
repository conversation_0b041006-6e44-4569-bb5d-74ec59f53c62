<template>
  <main class="container my-3 min-vh-100">
    <Card>
      <template #title>
        <div class="d-flex justify-content-between">
          <span>Update Configurations</span>
          <Button icon="pi pi-arrow-left" @click="backToConfiguration()" class="ms-auto" :label="'Back'" size="small"
            aria-label="Submit" link />
        </div>
      </template>
      <template #content>
        <ManageRepricer :createRepricerPayload="createRepricerPayload" :activeCurrencies="activeCurrencies"
          :onsubmit="updateRepricer" :isEdit="true" :isProgress="isProgress" v-on:onremount="reloadRepricer" />
      </template>
    </Card>
  </main>
</template>

<script>
import { showErrorMessage } from '@/helpers/utils';
import { useRepricerStore } from '@/stores/useRepricerStore';
import Card from 'primevue/card';
import Button from 'primevue/button';
import ManageRepricer from '@/components/repricer/ManageRepricer.vue';
import { useUserStore } from '@/stores/useUserStore';
import { useAuthStore } from '@/stores/useAuthStore';

export default {
  name: "Create Repricer",
  components: { Card, ManageRepricer, Button },
  data() {
    return {
      createRepricerPayload: {},
      isProgress: false,
      activeCurrencies: null
    }
  },
  computed: {
  },
  methods: {
    backToConfiguration() {
      if (this.$route.params.repricerId) {
        this.$router.push({ path: '/configuration/' + this.$route.params.repricerId })
      } else {
        this.$router.push({ path: '/configuration' })
      }
    },
    async updateRepricer() {
      this.isProgress = true;
      const repricer = useRepricerStore();
      const payload = { ...this.createRepricerPayload };
      const response = await repricer.updateRepricer(payload);
      if (response.isError) {
        showErrorMessage(this.$toast, response, 'Not able to update rePricer.')
      } else {
        await repricer.getRePricerById(this.createRepricerPayload.repricerUserID);
        this.$toast.add({ severity: 'success', summary: 'Updated successfully', detail: `${this.createRepricerPayload.repricerUserName} updated successfully.`, life: 3000 });
      }

      this.isProgress = false;
    },
    async reloadRepricer() {
      const repricer = useRepricerStore();
      this.createRepricerPayload = await repricer.getRePricerById(this.createRepricerPayload.repricerUserID);
    }
  },

  async mounted() {
    const rePricer = useRepricerStore();
    const repricerID = this.$route.params.repricerId || rePricer.repricer.repricerUserID;
    this.createRepricerPayload = await rePricer.getRePricerById(repricerID);
    this.activeCurrencies = await rePricer.getCurrency(repricerID);
  }
}

</script>
