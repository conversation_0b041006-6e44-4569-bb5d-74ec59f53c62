import moment from "moment";
import constants from "./constants";

const filters = {
	dateAndTime(value) {
		return moment(new Date(value)).format(constants.dateAndTimeFormatDisplay)
		// return this.dateAndTimeLocal(value)
	},
	dateAndTimeLocal(value) {
		try {
			const utcDateString = value.indexOf('Z') >= 0 ? value : value + "Z";
			const utcDate = new Date(utcDateString);

			const momentDate = moment(utcDate);
			const output = momentDate.format(constants.dateAndTimeFormatDisplay);

			if (new Date(output).toString().toLowerCase() == 'invalid date') {
				return moment(new Date(value)).format(constants.dateFormatDisplay)
			} else {
				return output
			}
		} catch (error) {
			return moment(new Date(value)).format(constants.dateFormatDisplay)
		}
	},
	date(value) {
		return moment(new Date(value)).format(constants.dateFormatDisplay)
	},
	dateShort(value) {
		return moment(new Date(value)).format(constants.dateFormatDisplayShort)
	},
	dateDifference(date1, date2, type) {
		var datePrev = moment(new Date(date1));
		var dateNext = moment(new Date(date2));
		var absoluteDiffInMilliseconds = Math.abs(dateNext.diff(datePrev));

		// Calculate individual differences
		var diffInDays = Math.floor(absoluteDiffInMilliseconds / (1000 * 60 * 60 * 24));
		var diffInHours = Math.floor((absoluteDiffInMilliseconds % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
		var diffInMinutes = Math.floor((absoluteDiffInMilliseconds % (1000 * 60 * 60)) / (1000 * 60));
		var diffInSeconds = Math.floor((absoluteDiffInMilliseconds % (1000 * 60)) / 1000);

		var message = "";

		if (diffInDays === 0 && diffInHours === 0 && diffInMinutes === 0 && diffInSeconds === 0) {
			message = "No time difference.";
		} else {
			if (diffInDays > 0) {
				message += diffInDays + " day" + (diffInDays > 1 ? "s" : "");
			}

			if (diffInHours > 0) {
				if (message !== "") {
					message += " and ";
				}
				message += diffInHours + " hour" + (diffInHours > 1 ? "s" : "");
			}

			if (diffInMinutes > 0 && diffInDays === 0) {
				if (message !== "") {
					message += " and ";
				}
				message += diffInMinutes + " minute" + (diffInMinutes > 1 ? "s" : "");
			}

			// if (diffInSeconds > 0 && diffInDays === 0 && diffInHours === 0) {
			// 	if (message !== "") {
			// 		message += " and ";
			// 	}
			// 	message += diffInSeconds + " second" + (diffInSeconds > 1 ? "s" : "");
			// }

			if (dateNext.isBefore(datePrev)) {
				message += " before.";
			} else {
				message += " later.";
			}
		}

		return message;

	},
	priceDisplay(price, currency, isRoundoff) {
		price = Number.parseFloat(price).toFixed(constants.priceDecimalLimit);
		if (isRoundoff) {
			price = Math.round(price);
		}

		const parts = price.toString().split(".");
		parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");

		return parts.join(".") + ' ' + currency
	},
	formatPrice(price) {
		// Convert price to a string and split it at the decimal point
		const parts = price.toString().split(".");

		// Insert commas every three digits from the right
		parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");

		// Join the parts back together with a decimal point
		return parts.join(".");
	},
	validatePassword(password) {
		const minLength = /.{8,}/;
		const hasLowercase = /[a-z]/;
		const hasUppercase = /[A-Z]/;
		const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/;
		let errorMessage = '';

		if (!minLength.test(password)) {
			errorMessage = 'Password must be at least 8 characters.';
			return { status: false, errorMessage };
		}
		if (!hasLowercase.test(password)) {
			errorMessage = 'Password must contain at least one lowercase letter.';
			return { status: false, errorMessage };
		}
		if (!hasUppercase.test(password)) {
			errorMessage = 'Password must contain at least one uppercase letter.';
			return { status: false, errorMessage };
		}
		if (!hasSpecialChar.test(password)) {
			errorMessage = 'Password must contain at least one special character.';
			return { status: false, errorMessage };
		}

		return { status: true, errorMessage };
	},

}

export default filters;