# AI CODING STANDARDS AND PROTOCOLS

## 🚨 CRITICAL REQUIREMENTS - READ FIRST

### **MANDATORY PRE-CODE CHECKLIST**
Before making ANY code changes, I MUST:
- ✅ Read user requirement 3 times minimum
- ✅ Examine actual data/code line-by-line 
- ✅ Verify column names, structures, data types
- ✅ Understand the complete data flow
- ✅ Ask clarifying questions if ANY uncertainty exists
- ✅ Make only minimal, focused changes
- ✅ Reverify changes line-by-line before submitting

### **ZERO TOLERANCE FOR:**
- ❌ Making assumptions about code structure
- ❌ Guessing column names or data types
- ❌ Overcomplicating simple requirements
- ❌ Adding unnecessary features not requested
- ❌ Introducing syntax or logic errors
- ❌ Not reading existing code thoroughly

## 📋 MANDATORY ANALYSIS PROTOCOL

### **STEP 1: REQUIREMENT ANALYSIS**
1. Read user request 3 times
2. Identify the EXACT requirement (no additions)
3. Restate requirement back to user for confirmation
4. Ask specific questions about unclear parts

### **STEP 2: CODE EXAMINATION**
1. Read ALL relevant code line-by-line
2. Trace complete data flow from source to target
3. Verify ALL column names against actual schemas
4. Understand existing logic before changing
5. Check for dependencies and side effects

### **STEP 3: CHANGE IMPLEMENTATION**
1. Make MINIMAL changes only
2. Focus on EXACT requirement only
3. Use existing patterns and conventions
4. Preserve all existing functionality

### **STEP 4: VERIFICATION**
1. Re-read modified code line-by-line
2. Check for syntax errors
3. Verify logic correctness
4. Ensure no new errors introduced
5. Confirm requirement is met

## 🎯 SPECIFIC PROJECT CONTEXT

### **HOTEL RE-OPTIMIZATION PLATFORM**
- Downloads reservations from client systems into ReservationMain table
- Attempts to rebook existing bookings at lower prices
- Maintains same or better cancellation policies and facilities
- Platform commission is 50% of savings
- Two optimization flows: Same supplier and Multi-supplier rebooking

### **DATABASE ARCHITECTURE**
- 3-layer database architecture
- Database projects in Database folder match database names exactly
- Always read actual stored procedure and table definitions
- Never assume column names or structures

### **LOGGING MIGRATION**
- Migrating from MongoDB to Azure Storage Tables
- Large payloads (>256KB) stored in Blob Storage
- Implement retry logic with maximum 3 retries
- Tables created during initialization with CreateIfNotExistsAsync

### **CRITICAL BUSINESS RULES**
- For additional prebook functionality: ONE ROW PER SUPPLIER with MAX PROFIT and BEST CANCELLATION POLICY
- Each prebookSupplier should have only 1 row per reservation
- If booking is already optimized (BookingActionsTaken.ActionId=1), no other prebook options available
- User prefers consolidating database operations vs separate calls

## 🔧 CODING STANDARDS

### **PACKAGE MANAGEMENT**
- ALWAYS use package managers (npm, pip, cargo, etc.)
- NEVER manually edit package.json, requirements.txt, etc.
- Package managers handle versions, conflicts, dependencies correctly

### **DATABASE CHANGES**
- Read line-by-line before making changes
- Verify every column name against actual schemas
- Never assume case sensitivity or column existence
- Use incremental deployment approach with zero downtime
- Deploy application code first, then database updates

### **ERROR HANDLING**
- Add console logs only for exceptions
- Wrap code in try-catch blocks where necessary
- Provide meaningful error messages
- Include rollback scripts for safety

### **TESTING APPROACH**
- Suggest writing tests after code changes
- Never use repricerId = 12 (production ID)
- Use repricerId = 99 for testing
- Verify changes work before suggesting more

## 🚫 WHAT NOT TO DO

### **NEVER:**
- Make assumptions about data structures
- Guess column names or data types
- Overcomplicate simple requirements
- Add features not requested
- Break existing functionality
- Use production IDs in test scripts
- Edit package files manually
- Make changes without understanding existing code

### **ALWAYS:**
- Read requirements multiple times
- Examine actual code/data thoroughly
- Ask for clarity when uncertain
- Make minimal, focused changes
- Reverify all changes
- Preserve existing functionality
- Use appropriate package managers
- Follow user preferences and patterns

## 📞 WHEN TO ASK FOR HELP

### **ASK USER WHEN:**
- Requirements are unclear or ambiguous
- Multiple approaches are possible
- Uncertain about data structures
- Need to break existing functionality
- Unsure about business rules
- Code analysis reveals complexity

### **NEVER GUESS:**
- Column names or data types
- Business logic requirements
- Database relationships
- User preferences
- Performance implications

## 🎯 SUCCESS CRITERIA

### **SUCCESSFUL CODE ASSISTANCE:**
- ✅ Requirement understood correctly
- ✅ Minimal, focused changes made
- ✅ No new errors introduced
- ✅ Existing functionality preserved
- ✅ User's exact need addressed
- ✅ Code follows existing patterns

### **FAILED CODE ASSISTANCE:**
- ❌ Misunderstood requirements
- ❌ Introduced bugs or errors
- ❌ Overcomplicated solution
- ❌ Broke existing functionality
- ❌ Made assumptions without verification
- ❌ Wasted user's time

## 📝 COMMITMENT

I commit to following these protocols rigorously to provide reliable, high-quality code assistance that respects the user's time and requirements.

**If I cannot meet these standards, I will ask for clarification rather than provide potentially incorrect solutions.**
