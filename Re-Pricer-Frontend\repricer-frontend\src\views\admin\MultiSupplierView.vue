<script>
import { useRepricerStore } from '@/stores/useRepricerStore';

import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import { useUserStore } from '@/stores/useUserStore';
import But<PERSON> from 'primevue/button';
import Dialog from 'primevue/dialog';
import Loader from '@/components/Loader.vue';
import { useAuthStore } from '@/stores/useAuthStore';
import Panel from 'primevue/panel';


export default {
    name: "MultiSupplier",
    components: {
        DataTable, Column,
        Button,
        Dialog,
        Loader,
        Panel
    },
    data() {
        return {
            isDeveloper: false,
            MultiSupplierData: null,
            visible: false,
            selectedCustomer: null,
            isLoading: true,
            panelStyle: {
                header: {
                    class: "p-0"
                },
                content: {
                    class: "p-0"
                }
            },
            optimizationTypes: {
                "0": "Demo",
                "1": "Demo",
                "2": "Semi-Automation",
                "3": "Automation",
            }
        }
    },
    computed: {
    },
    methods: {
        showBreakup(data) {
            this.visible = true;
            this.selectedCustomer = data;
            console.log(data)
        },
        async initPage(isFresh) {
            this.isLoading = true;
            const auth = useAuthStore()
            const reprice = useRepricerStore();
            const users = useUserStore();
            this.isDeveloper = auth.userInfo.roleName.indexOf("Developer") >= 0;
            const allUsers = await users.getAllUsers();
            const params = {
                RepricerId: auth.isSuperAdmin ? 0 : reprice.repricer.repricerUserID
            }

            if (isFresh) {
                params["isCached"] = false
            }
            const multiSupplierData = await reprice.getMultiSupplier(params);

            if (!multiSupplierData.isError) {
                const summary = [...multiSupplierData.multiSupplierReports]
                const merged = []
                summary.map((sum) => {
                    const customer = allUsers.find(user => user.rePricerDetail.repricerUserID == sum.rePricerId);
                    const breakup = multiSupplierData.multiSupplierFailedLogs.filter((b) => b.rePricerId == sum.rePricerId)

                    merged.push({
                        summary: sum,
                        customer,
                        breakup
                    })

                    return sum
                })

                this.MultiSupplierData = merged;
                this.isLoading = false;
            } else {
                this.isLoading = false;
            }
        }
    },
    async mounted() {
        this.initPage()
    },
    watch: {

    }
}

</script>

<template>
    <main class="container d-flex my-3 flex-column">
        <div class="flex justify-content-end mb-3">
            <Button v-if="isDeveloper" icon="pi pi-refresh" label="Fetch latest data" @click="initPage(true)"></Button>
        </div>
        <DataTable :value="MultiSupplierData" :loading="isLoading" showGridlines size="small">
            <template #header>
                Multi-Supplier Reservation Report
            </template>
            <template #empty>
                <div class="text-center">No records found.</div>
            </template>
            <template #loading>
                <div class="bg-white d-flex flex-column gap-5 my-5 p-4 shadow pt-0 text-center">
                    <Loader />
                    Please wait...
                </div>
            </template>
            <Column field="customer.rePricerDetail.repricerUserName" header="Customer ID">
                <template #body="{ data }">
                    <strong>

                        <router-link class="text-primary-900"
                            :to="`/dashboard/${data.customer.rePricerDetail.repricerUserID}`">{{
                                data.customer.rePricerDetail.repricerUserName }}</router-link>
                        <Divider class="d-none d-md-block my-2" />
                        <div class="align-items-end d-flex flex-column fz-r0_9 gap-2 flex-md-row">
                            <small>Id : {{ data.customer.rePricerDetail.repricerUserID }}</small>
                            <small class="d-none d-md-block">|</small>
                            <small>Type : {{ optimizationTypes[data.customer.rePricerDetail.optimizationType] }}</small>
                        </div>
                    </strong>
                </template>
            </Column>
            <Column field="summary.totalRefundable" header="Refundable"></Column>
            <Column field="summary.filteredOut" header="Not Qualified">
                <template #body="{ data }">
                    {{ data.summary.filteredOut || "--" }}
                </template>
            </Column>
            <Column field="summary.failed" header="Failed" style="width: 400px;">
                <template #body="{ data }">
                    <Panel :header="data.summary.failed.toString()" toggleable class="border-0" :pt="panelStyle"
                        v-if="data.breakup.length > 0" collapsed>
                        <div class="bg-red-100 d-flex flex-column fz-r0_9 justify-content-between p-2 text-red-900">
                            <div class="d-flex gap-3 justify-content-between"
                                v-for="log in data.breakup.sort((a, b) => b.reservationCount - a.reservationCount)">
                                <span class="fz-r0_8 text-capitalize text-nowrap">{{ log.reasonMessage }}</span>
                                <span class="text-end text-nowrap text-truncate" style="width: 50px;">{{
                                    log.reservationCount }}</span>
                            </div>
                        </div>
                    </Panel>
                    <div v-else>--</div>
                </template>
            </Column>
            <Column field="summary.success" header="Sucess" class="align-top" style="min-width: 220px;">
                <template #body="{ data }">
                    <Panel :header="data.summary.success.toString()" v-if="data.summary.success > 0" toggleable
                        :pt="panelStyle" class="border-0" collapsed>
                        <div class="d-flex fz-r0_9 justify-content-between flex-column bg-green-100 text-green-900 p-2">
                            <div class="d-flex gap-3 justify-content-between px-1">
                                <span class="fz-r0_8 text-capitalize text-nowrap">Same Supplier</span>
                                <span class="text-end text-truncate" style="width: 50px;">{{ data.summary.sameSupplier
                                    }}</span>
                            </div>
                            <div class="d-flex gap-3 justify-content-between px-1">
                                <span class="fz-r0_8 text-capitalize text-nowrap">Different Supplier</span>
                                <span class="text-end text-truncate" style="width: 50px;">{{
                                    data.summary.differentSupplierInView || '--' }}</span>
                            </div>
                        </div>
                    </Panel>
                    <div v-else>--</div>
                </template>
            </Column>
            <Column field="summary.reportedInDashboard" header="Reported in Dashbaord">
                <template #body="{ data }">
                    <router-link class="text-primary-900"
                        :to="`/optimizations/using-room-mapping/${data.customer.rePricerDetail.repricerUserID}`">{{
                            data.summary.reportedInDashboard || "--" }}</router-link>

                </template>
            </Column>
        </DataTable>
        <Dialog v-if="selectedCustomer" v-model:visible="visible" modal
            :header="`${selectedCustomer.customer.rePricerDetail.repricerUserName} Breakup`"
            :style="{ width: '40rem' }">
            <div class="d-flex flex-column gap-3">
                <div v-for="breakup in selectedCustomer.breakup" class="d-flex flex-column">
                    <div class="fz-r0_8">{{ breakup.reasonMessage }}</div>
                    <strong class="fs-4">{{ breakup.reservationCount }}</strong>
                </div>
            </div>

        </Dialog>
    </main>
</template>

<style></style>