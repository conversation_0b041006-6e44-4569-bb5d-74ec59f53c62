<template>
    <Panel header="" collapsed>
        <template #header>
            <div class="align-items-md-center flex flex-column flex-fill flex-md-row gap-2 justify-content-between">
                <SelectButton v-model="selectedReportType" :options="roleBasedReportTypes" optionLabel="value"
                    dataKey="value" aria-labelledby="custom" @change="updateReportTab" :allowEmpty="false">
                </SelectButton>
            </div>
        </template>
        <div class="d-flex flex-wrap gap-3">
            <div class="flex flex-column flex-fill flex-md-grow-0 gap-1">
                <label for="repricerid" class="fz-r0_9 text-muted">Repricer</label>
                <Dropdown v-model="requestBody.repricerId" id="repricerid" :options="repricerList" optionLabel="name"
                    placeholder="Select Repricer" class="w-full md:w-14rem" optionValue="code" />
            </div>

            <div class="flex flex-column flex-fill flex-md-grow-0 gap-1">
                <label for="reservationId" class="fz-r0_9 text-muted">Reservation Id</label>
                <InputText v-model="requestBody.reservationId" id="reservationId" />
            </div>

            <div class="flex flex-column flex-fill flex-md-grow-0 gap-1">
                <label for="reservationStatus" class="fz-r0_9 text-muted">Reservation Status</label>
                <Dropdown v-model="requestBody.reservationStatus" id="reservationStatus" optionValue="code" showClear
                    :options="reservationStatus" optionLabel="name" placeholder="Select Status"
                    class="w-full md:w-14rem" />
            </div>

            <div class="flex flex-column flex-fill flex-md-grow-0 gap-1">
                <label for="preBookDaterange" class="fz-r0_9 text-muted">PreBook Between</label>
                <Calendar v-model="preBookDaterange" id="preBookDaterange" selectionMode="range" :manualInput="false"
                    :hideOnRangeSelection="true" showButtonBar />
            </div>

            <div class="flex flex-column flex-fill flex-md-grow-0 gap-1">
                <label for="bookingFromDate" class="fz-r0_9 text-muted">Booking Between</label>
                <Calendar v-model="bookingDaterange" id="bookingFromDate" selectionMode="range" :manualInput="false"
                    :hideOnRangeSelection="true" showButtonBar />
            </div>

            <div class="flex flex-column flex-fill flex-md-grow-0 gap-1">
                <label for="checkInFromDate" class="fz-r0_9 text-muted">Check-In Between</label>
                <Calendar v-model="checkInDaterange" id="checkInFromDate" selectionMode="range" :manualInput="false"
                    :hideOnRangeSelection="true" showButtonBar />
            </div>

            <div class="flex flex-column flex-fill flex-md-grow-0 gap-1">
                <label for="profitFrom" class="fz-r0_9 text-muted">Profit From</label>
                <InputNumber v-model="requestBody.profitFrom" inputId="profitFrom" :useGrouping="false" />
            </div>

            <div class="flex flex-column flex-fill flex-md-grow-0 gap-1">
                <label for="profitTo" class="fz-r0_9 text-muted">Profit To</label>
                <InputNumber v-model="requestBody.profitTo" inputId="profitTo" :useGrouping="false" />
            </div>

            <div class="flex flex-column flex-fill flex-md-grow-0 gap-1" v-if="suppliersList.length > 0">
                <label for="suppliers" class="fz-r0_9 text-muted">Suppliers</label>
                <Dropdown v-model="requestBody.suppliers" id="suppliers" :options="suppliersList" optionLabel="name"
                    showClear placeholder="Select Status" class="w-full md:w-14rem" />
            </div>

            <div class="flex flex-column flex-fill flex-md-grow-0 gap-1">
                <label for="cpDaysGain" class="fz-r0_9 text-muted">Canc. policy Days Gain</label>
                <InputNumber v-model="requestBody.cpDaysGain" inputId="cpDaysGain" :useGrouping="false" />
            </div>
        </div>
        <template #footer>
            <div class="flex flex-wrap align-items-center justify-content-between gap-3">
                <div class="flex align-items-center gap-2 ms-auto">
                    <Button size="small" icon="pi pi-filter" label="Filter" text @click="applyFilter()"></Button>
                    <Button size="small" icon="pi pi-times" severity="secondary" label="Clear" text
                        @click="resetFilters()"></Button>
                </div>
            </div>
        </template>
    </Panel>
    <Divider />
</template>

<script>
import Button from 'primevue/button';
import ButtonGroup from 'primevue/buttongroup';
import Divider from 'primevue/divider';
import Panel from 'primevue/panel';
import Dropdown from 'primevue/dropdown';
import InputText from 'primevue/inputtext';
import Calendar from 'primevue/calendar';
import Slider from 'primevue/slider';
import InputNumber from 'primevue/inputnumber';
import Checkbox from 'primevue/checkbox';
import { useUserStore } from '@/stores/useUserStore';
import moment from 'moment';
import SelectButton from 'primevue/selectbutton';
import { useAuthStore } from '@/stores/useAuthStore';
export default {
    name: "ReportFilters",
    components: { Panel, Divider, Dropdown, InputText, Calendar, Slider, InputNumber, Checkbox, Button, ButtonGroup, SelectButton },
    props: [
        "notificationType"
    ],
    emits: ["updateReport"],
    data() {
        return {
            // Repricer ID
            userStore: null,
            suppliersList: [],
            repricerList: [],
            reservationStatus: [
                { name: 'OK', code: 'OK' },
                { name: 'NOTOK', code: 'NOTOK' },
            ],
            selectedReportType: null,
            reportTypes: [
                // {
                //     value: 'All',
                //     route: 'report',
                //     params: {
                //         reportType: '',
                //         "isReservationActionTaken": null,
                //     }
                // },
                {
                    value: 'Optimized',
                    route: 'optimized',
                    role: ["*"],
                    params: {
                        reportType: '',
                        "isReservationActionTaken": true,
                    }
                },
                {
                    value: 'Active',
                    route: 'active',
                    role: ["*"],
                    params: {
                        reportType: 'Prebook',
                        "isReservationActionTaken": null,
                    }
                },
                {
                    value: 'Cancellation policy',
                    route: 'cancellation-policy',
                    role: ["*"],
                    params: {
                        reportType: 'CancellationEdgeCase',
                        "isReservationActionTaken": null,
                    }
                },
                {
                    value: 'Cancellation policy Applicable',
                    route: 'cancellation-policy-applicable',
                    role: ["*"],
                    params: {
                        reportType: 'CancellationChargesApplicable',
                        "isReservationActionTaken": null,
                    }
                },
                {
                    value: 'Better Cancellation policy',
                    route: 'cancellation-policy-better',
                    role: ["*"],
                    params: {
                        reportType: 'NoOrLessGainButBetterCancellation',
                        "isReservationActionTaken": null,
                    }
                },
                {
                    value: 'Price Edge',
                    route: 'price-threshold',
                    role: ["*"],
                    params: {
                        reportType: 'PriceEdgeCase',
                        "isReservationActionTaken": null,
                    }
                },
                {
                    value: 'Cross Supplier - Within Criteria',
                    route: 'using-room-mapping',
                    role: ["Developer"],
                    params: {
                        reportType: 'UsingRoomMapping'
                    }
                },
                {
                    value: 'Cross Supplier - Without Criteria',
                    route: 'using-room-mapping-without-criteria',
                    role: ["Developer"],
                    params: {
                        reportType: 'UsingRoomMapping1'
                    }
                },
            ],
            preBookDaterange: null,
            bookingDaterange: null,
            checkInDaterange: null,
            profitRange: [0, 0],
            reportType: "",
            requestBody: {
                "repricerId": parseInt(this.$route.params.repricerId),
                "reservationId": null,
                "reservationStatus": null,
                "suppliers": null,
                "cpDaysGain": null,
                "isPrebookLive": false,
                "isReservationActionTaken": null,
                "reportType": null,
                "pageNumber": 1,
                "pageSize": 20,
                "profitFrom": null,
                "profitTo": null
            },
            isSuperAdmin: null,
            userInfo: null
        }
    },
    computed: {
        roleBasedReportTypes() {
            if (this.userInfo && this.reportTypes) {
                return this.reportTypes.filter(action => {
                    // If the action role is "*" (wildcard), allow for all
                    if (action.role.includes("*")) {
                        return true;
                    }

                    // Check if there is an intersection between userRoles and action roles
                    return action.role.some(role => this.userInfo.roleName.includes(role));
                });
            }
            return []
        },
        requestParam() {
            let params = { ...this.requestBody };
            if (this.selectedReportType) {
                params = {
                    ...params,
                    ...this.selectedReportType.params
                }
            } else {
                params = {
                    ...params,
                    ...this.reportTypes[0].params
                }
            }
            if (this.preBookDaterange) {
                params["preBookFromDate"] = moment(this.preBookDaterange[0]).format("YYYY-MM-DD");
                params["preBookToDate"] = moment(this.preBookDaterange[1]).format("YYYY-MM-DD");
            } else {
                params["preBookFromDate"] = null;
                params["preBookToDate"] = null;
            }
            if (this.bookingDaterange) {
                params["bookingFromDate"] = moment(this.bookingDaterange[0]).format("YYYY-MM-DD");
                params["bookingToDate"] = moment(this.bookingDaterange[1]).format("YYYY-MM-DD");
            } else {
                params["bookingFromDate"] = null;
                params["bookingToDate"] = null;
            }
            if (this.checkInDaterange) {
                params["checkInFromDate"] = moment(this.checkInDaterange[0]).format("YYYY-MM-DD");
                params["checkInToDate"] = moment(this.checkInDaterange[1]).format("YYYY-MM-DD");
            } else {
                params["checkInFromDate"] = null;
                params["checkInToDate"] = null;
            }
            return params
        }
    },
    methods: {
        updateReportTab(data) {
            // console.log(data)
            let path = "/optimizations/" + data.value.route;
            if (this.$route.params.repricerId) {
                path = path + "/" + this.$route.params.repricerId;
            }
            this.$router.push(path);
            this.$emit('updateReport', this.requestParam);
        },
        resetFilters() {
            this.requestBody = {
                ...this.requestBody,
                "reservationId": null,
                "reservationStatus": null,
                "suppliers": null,
                "cpDaysGain": null,
                "isPrebookLive": false,
                "isReservationActionTaken": null,
                "reportType": null,
                "preBookFromDate": null,
                "preBookToDate": null,
                "bookingFromDate": null,
                "bookingToDate": null,
                "checkInFromDate": null,
                "checkInToDate": null,
                "profitFrom": null,
                "profitTo": null,
            }
            this.preBookDaterange = null;
            this.bookingDaterange = null;
            this.checkInDaterange = null;
            this.profitRange = null;
            this.applyFilter();
        },
        applyFilter() {
            console.log('emit', this.requestParam)
            this.$emit('updateReport', this.requestParam);
        }
    },
    async mounted() {
        const auth = useAuthStore();
        this.isSuperAdmin = auth.isSuperAdmin;
        this.userInfo = auth.userInfo;
        this.userStore = useUserStore();
        let selectedTab = 0;
        if (this.notificationType) {
            this.requestBody.reportType = this.notificationType;
            selectedTab = this.reportTypes.findIndex(t => t.params.reportType == this.notificationType);
            selectedTab = selectedTab < 0 ? 0 : selectedTab;
        }
        this.selectedReportType = this.reportTypes[selectedTab];
        this.repricerList = this.userStore.users.map(user => {
            return {
                name: user.rePricerDetail.repricerUserName,
                code: parseInt(user.rePricerDetail.repricerUserID)
            }
        })
    }
}

</script>