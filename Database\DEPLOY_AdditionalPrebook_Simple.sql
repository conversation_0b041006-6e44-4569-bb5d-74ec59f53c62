-- =============================================
-- SIMPLE DEPLOYMENT SCRIPT FOR ADDITIONAL PREBOOK FUNCTIONALITY
-- Author: Augment Agent
-- Date: 2025-01-27
-- Description: Deploy database changes and test with RepricerId = 1
-- =============================================

USE [rpndb]
GO

PRINT '=========================================='
PRINT 'ADDITIONAL PREBOOK DEPLOYMENT - SIMPLE VERSION'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

-- =============================================
-- STEP 1: CREATE TABLE (if not exists)
-- =============================================
PRINT 'STEP 1: Creating table ReservationReportDetailsAdditionalPrebook...'

-- Execute the table creation script
-- NOTE: Run this file first: Database/rpndb/dbo/Tables/ReservationReportDetailsAdditionalPrebook.sql

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ReservationReportDetailsAdditionalPrebook]') AND type in (N'U'))
BEGIN
    PRINT '❌ ERROR: Table ReservationReportDetailsAdditionalPrebook does not exist!'
    PRINT 'Please run this script first: Database/rpndb/dbo/Tables/ReservationReportDetailsAdditionalPrebook.sql'
    RETURN
END
ELSE
BEGIN
    PRINT '✅ Table ReservationReportDetailsAdditionalPrebook exists'
END

-- =============================================
-- STEP 2: CREATE STORED PROCEDURES
-- =============================================
PRINT 'STEP 2: Checking stored procedures...'

-- Check usp_upd_reservationreport_AdditionalPrebook
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_upd_reservationreport_AdditionalPrebook]') AND type in (N'P'))
BEGIN
    PRINT '❌ ERROR: Procedure usp_upd_reservationreport_AdditionalPrebook does not exist!'
    PRINT 'Please run this script first: Database/rpndb/dbo/Stored Procedures/usp_upd_reservationreport_AdditionalPrebook.sql'
    RETURN
END
ELSE
BEGIN
    PRINT '✅ Procedure usp_upd_reservationreport_AdditionalPrebook exists'
END

-- Check usp_get_AdditionalPrebookOptions
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_get_AdditionalPrebookOptions]') AND type in (N'P'))
BEGIN
    PRINT '❌ ERROR: Procedure usp_get_AdditionalPrebookOptions does not exist!'
    PRINT 'Please run this script first: Database/rpndb/dbo/Stored Procedures/usp_get_AdditionalPrebookOptions.sql'
    RETURN
END
ELSE
BEGIN
    PRINT '✅ Procedure usp_get_AdditionalPrebookOptions exists'
END

-- =============================================
-- STEP 3: TEST WITH REPRICER ID = 1
-- =============================================
PRINT ''
PRINT '=========================================='
PRINT 'STEP 3: TESTING WITH REPRICER ID = 1'
PRINT '=========================================='

DECLARE @TestRepricerId INT = 1;
DECLARE @DataCount INT;
DECLARE @AdditionalPrebooksCount INT;

-- Check if we have data for repricer 1
SELECT @DataCount = COUNT(*)
FROM ReservationTable 
WHERE RepricerID = @TestRepricerId;

PRINT 'Found ' + CAST(@DataCount AS VARCHAR(10)) + ' records in ReservationTable for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10));

IF @DataCount = 0
BEGIN
    SELECT @DataCount = COUNT(*)
    FROM ReservationTablelog 
    WHERE RepricerID = @TestRepricerId;
    
    PRINT 'Found ' + CAST(@DataCount AS VARCHAR(10)) + ' records in ReservationTablelog for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10));
END

IF @DataCount > 0
BEGIN
    PRINT 'Executing additional prebook procedure for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10)) + '...'
    
    -- Clear any existing test data
    DELETE FROM ReservationReportDetailsAdditionalPrebook WHERE Repricerid = @TestRepricerId;
    PRINT 'Cleared existing test data'
    
    -- Execute the additional prebook procedure
    BEGIN TRY
        EXEC dbo.usp_upd_reservationreport_AdditionalPrebook @Repricerid = @TestRepricerId, @Reservationid = NULL;
        PRINT '✅ Procedure executed successfully'
    END TRY
    BEGIN CATCH
        PRINT '❌ ERROR executing procedure: ' + ERROR_MESSAGE();
        RETURN
    END CATCH
    
    -- Check results
    SELECT @AdditionalPrebooksCount = COUNT(*)
    FROM ReservationReportDetailsAdditionalPrebook
    WHERE Repricerid = @TestRepricerId;
    
    PRINT 'Additional prebooks created: ' + CAST(@AdditionalPrebooksCount AS VARCHAR(10));
    
    IF @AdditionalPrebooksCount > 0
    BEGIN
        PRINT '✅ SUCCESS: Additional prebooks were created!'
        
        -- Show sample results
        PRINT ''
        PRINT 'SAMPLE RESULTS (Top 5):'
        SELECT TOP 5
            ReportId,
            Reservationid,
            PrebookRank,
            prebooksupplier,
            CAST(Profit AS DECIMAL(10,2)) as Profit,
            CAST(Createdate AS DATE) as CreateDate,
            PrimaryPrebookId
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId
        ORDER BY Reservationid, PrebookRank;
        
        -- Show summary by rank
        PRINT ''
        PRINT 'SUMMARY BY RANK:'
        SELECT 
            PrebookRank,
            COUNT(*) as Count,
            COUNT(DISTINCT prebooksupplier) as UniqueSuppliers,
            AVG(Profit) as AvgProfit
        FROM ReservationReportDetailsAdditionalPrebook
        WHERE Repricerid = @TestRepricerId
        GROUP BY PrebookRank
        ORDER BY PrebookRank;
        
    END
    ELSE
    BEGIN
        PRINT '⚠️ WARNING: No additional prebooks were created.'
        PRINT 'This could be normal if:'
        PRINT '   - No reservations have multiple prebook options'
        PRINT '   - All prebooks are from the same supplier'
        PRINT '   - Data doesn''t meet diversity criteria'
        PRINT '   - All bookings are already optimized'
    END
END
ELSE
BEGIN
    PRINT '⚠️ WARNING: No data found for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10));
    PRINT 'Try using a different RepricerId that has data'
END

-- =============================================
-- STEP 4: VERIFICATION QUERIES
-- =============================================
PRINT ''
PRINT '=========================================='
PRINT 'STEP 4: VERIFICATION QUERIES'
PRINT '=========================================='

-- Query 1: Table structure
PRINT 'Table structure verification:'
SELECT 
    COUNT(*) as TotalColumns,
    SUM(CASE WHEN COLUMN_NAME IN ('PrebookRank', 'PrimaryPrebookId') THEN 1 ELSE 0 END) as NewColumns
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ReservationReportDetailsAdditionalPrebook';

-- Query 2: Index verification
PRINT 'Index verification:'
SELECT 
    COUNT(*) as TotalIndexes
FROM sys.indexes 
WHERE object_id = OBJECT_ID('ReservationReportDetailsAdditionalPrebook')
  AND name IS NOT NULL;

-- Query 3: Data verification for test repricer
PRINT 'Data verification for RepricerId = ' + CAST(@TestRepricerId AS VARCHAR(10)) + ':'
SELECT 
    COUNT(*) as TotalRecords,
    COUNT(DISTINCT Reservationid) as UniqueReservations,
    COUNT(DISTINCT prebooksupplier) as UniqueSuppliers,
    MIN(PrebookRank) as MinRank,
    MAX(PrebookRank) as MaxRank
FROM ReservationReportDetailsAdditionalPrebook
WHERE Repricerid = @TestRepricerId;

PRINT ''
PRINT '=========================================='
PRINT 'DEPLOYMENT AND TESTING COMPLETED!'
PRINT 'Date: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT '=========================================='

-- =============================================
-- MANUAL TESTING QUERIES
-- =============================================
PRINT ''
PRINT 'MANUAL TESTING QUERIES:'
PRINT '-- Use these queries to manually verify the functionality --'
PRINT ''
PRINT '-- Query A: Compare primary vs additional prebooks'
PRINT 'SELECT '
PRINT '    p.Reservationid,'
PRINT '    p.prebooksupplier as PrimarySupplier,'
PRINT '    CAST(p.Profit AS DECIMAL(10,2)) as PrimaryProfit,'
PRINT '    a.PrebookRank,'
PRINT '    a.prebooksupplier as AdditionalSupplier,'
PRINT '    CAST(a.Profit AS DECIMAL(10,2)) as AdditionalProfit'
PRINT 'FROM ReservationReportDetails p'
PRINT 'INNER JOIN ReservationReportDetailsAdditionalPrebook a'
PRINT '    ON p.Reservationid = a.Reservationid AND p.Repricerid = a.Repricerid'
PRINT 'WHERE p.Repricerid = 1'
PRINT 'ORDER BY p.Reservationid, a.PrebookRank;'
PRINT ''
PRINT '-- Query B: Test the get procedure'
PRINT 'EXEC dbo.usp_get_AdditionalPrebookOptions @RepricerId = 1;'
